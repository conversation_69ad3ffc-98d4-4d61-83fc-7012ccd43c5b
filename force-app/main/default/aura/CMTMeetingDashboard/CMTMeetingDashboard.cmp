<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global">
    <!-- <aura:handler name="init" value="{!this}" action="{!c.initRecords}"/>     -->
    <aura:attribute name="ViewName" type="String" default="Admin" />
    <aura:attribute name="recordId" type="String" default="Admin" />
    <aura:attribute name="TabName" type="String" default="Dashboard" />
    <div>
        <div class="dashboard">
            <div class="notification">
                <aura:if isTrue="{!v.TabName == 'Dashboard'}">
                    <c:cmtDashboardStatus modulename="meeting" viewname="{!v.ViewName}" onactioncall="{!c.actionHandler}" viewId="6" />
                </aura:if>
            </div>
            <aura:if isTrue="{!v.TabName == 'Dashboard'}">
                <aura:if isTrue="{!v.ViewName == 'Admin'}">
                    <c:DT_Table showSerialNumber="false" listname="Management_Review_Dashboard" editActionPlan="true" pageSize="5" showAddMeetingButton="true" showMeetingActions="true" showCompletedMeetingMinutes="true" />
                </aura:if>
                <!--Admin-->
                <aura:if isTrue="{!v.ViewName == 'actionClosed'}">
                    <c:DT_Table showSerialNumber="false" listname="Action_Plan_Closed" showAddMeetingButton="true" showMeetingMinutes="true" />
                </aura:if>
                <!--Owner View -->
            </aura:if>

            <aura:if isTrue="{!v.TabName == 'Meeting Minutes'}">
                <aura:if isTrue="{!v.ViewName == 'Admin'}">
                    <c:DT_Table listname="Management_Review_Dashboard" title="Minutes of Meeting" showMaintenanceActions="true" showAssetButton="true" showAssetEditAction="true" showMeetingMinutes="true" />
                </aura:if>
                <!--Approver View -->
                <aura:if isTrue="{!or(v.ViewName == 'Owner',v.ViewName == 'Approver')}">
                    <c:DT_Table listname="Management_Review_Dashboard" pageSize="5" showMaintenanceActions="true" showMeetingMinutes="true" />
                </aura:if>
                <!--Owner View -->
            </aura:if>
        </div>
    </div>
</aura:component>