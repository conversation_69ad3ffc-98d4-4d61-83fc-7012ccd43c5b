<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global">
    <!-- <aura:handler name="init" value="{!this}" action="{!c.initRecords}"/>     -->
    <aura:attribute name="ViewName" type="String" default="Admin" />
    <aura:attribute name="TabName" type="String" default="CORRECTIVE ACTION" />
    <aura:attribute name="sortdirectionasc" type="Boolean" default="True" />

    <div>
        <div class="dashboard">
            <div class="notification">
                <aura:if isTrue="{!v.TabName == 'CORRECTIVE ACTION'}">
                    <c:cmtDashboardStatus modulename="cc" viewname="{!v.ViewName}" onactioncall="{!c.actionHandler}" viewId="17" />
                </aura:if>
            </div>

            <aura:if isTrue="{!v.TabName == 'CORRECTIVE ACTION'}">
                <aura:if isTrue="{!v.ViewName == 'Admin'}">
                    <c:DT_Table listname="Corrective_Action_Log" showCALButton="true" showCAEditAction="true" sortdirectionasc="{!v.sortdirectionasc}" />
                </aura:if>
                <!--Admin-->
                <aura:if isTrue="{!v.ViewName == 'CloseCorrective'}">
                    <c:DT_Table listname="Closed_Corrective_Action_Log" sortdirectionasc="{!v.sortdirectionasc}" />
                </aura:if>
                <!--Owner View -->
            </aura:if>
        </div>
    </div>
</aura:component>