<aura:component controller="CMT_AssetDashboardCtrl"
    implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
    access="global">
    <aura:handler name="init" value="{!this}" action="{!c.initRecords}" />
    <aura:attribute name="ViewName" type="String" default="Admin" />
    <aura:attribute name="TabName" type="String" default="Dashboard" />
    <aura:attribute name="AllowAccess" type="boolean" default="false" />
    <div>
        <div class="dashboard">
            <div class="notification">
                <aura:if isTrue="{!v.TabName == 'Dashboard'}">
                    <c:cmtDashboardStatus modulename="training" viewname="{!v.ViewName}"
                        onactioncall="{!c.actionHandler}" viewId="8" />
                </aura:if>
            </div>
            <aura:if isTrue="{!v.TabName == 'Dashboard'}">
                <aura:if isTrue="{!v.ViewName == 'Admin'}">
                    <c:CMTDashboardTrainingTab></c:CMTDashboardTrainingTab>
                    <!-- <aura:if isTrue="{!v.AllowAccess == false}">
                        <c:DT_Table listname="My_Training_Request_Employee" showEmployeeTrainingEdit="true"
                            showTrainingEditAction="false" />
                    </aura:if> -->
                </aura:if>
                <!--Admin-->
                <aura:if isTrue="{!v.ViewName == 'TRAININGMATRIX'}">
                    <c:DT_Table listname="Training_Dashboard_Overdue" showTrainingDashboardButtons="false"
                        showTrainingActions="true" showEmployeeTrainingEdit="true" />
                </aura:if>
                <!--Owner View -->
                <aura:if isTrue="{!v.ViewName == 'MYTRAINING'}">
                    <c:DT_Table listname="Training_Dashboard" showTrainingApprovalAction="false"
                        showTrainingEditAction="true" showTrainingDashboardButtons="true" showTrainingActions="false" />
                </aura:if>
                <!--Owner View -->
            </aura:if>
        </div>
    </div>
</aura:component>