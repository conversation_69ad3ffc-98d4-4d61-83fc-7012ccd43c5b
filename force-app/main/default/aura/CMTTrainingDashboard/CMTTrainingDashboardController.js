({
    initRecords : function(component, event, helper) {
        
        var action = component.get("c.checkTrainingAccess");
        action.setCallback(this, function(response) {
            if ( response.getState() === "SUCCESS") {
                if(response.getReturnValue() == 'true') {
                    component.set('v.AllowAccess',true);
                } else {
                   component.set('v.AllowAccess',false);
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);

    },

    actionHandler : function(component, event, helper) {
        component.set('v.ViewName',event.getParam('targetName'));
    }
})