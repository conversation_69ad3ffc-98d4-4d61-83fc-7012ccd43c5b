({
    

    initRecords : function(component, event, helper) {


        var isModuleMan = component.get('c.checkModuleManager');
                isModuleMan.setCallback(this,function(response) { 
                    var isModuleManagers = response.getReturnValue();
                    
                    component.set('v.is_moduleManager',isModuleManagers);
                });
                $A.enqueueAction(isModuleMan);
            
                var isSuperVisor = component.get('c.checkIfUserIsSupervisor');
                isSuperVisor.setCallback(this,function(response) { 
                    var isSuperVisor = response.getReturnValue();
                    
                    component.set('v.is_supervisor',isSuperVisor);
                });
                $A.enqueueAction(isSuperVisor);
        
            $A.get('e.force:refreshView').fire();
            
        },
    myAction : function(component, event, helper) {

    }
})