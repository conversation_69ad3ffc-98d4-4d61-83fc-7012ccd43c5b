<aura:component controller="DT_TableController" implements="force:hasRecordId">

    
    <aura:handler name="init" value="{!this}" action="{!c.initRecords}" />
    <aura:attribute name="is_moduleManager" type="boolean" default="false" />
    <aura:attribute name="is_supervisor" type="boolean" default="false" />
    <lightning:tabset>
        <lightning:tab label="My Trainings">
            <c:DT_Table listname="Training_Dashboard_My_View" showTrainingDashboardButtons="true"
            showTrainingEditAction="true"></c:DT_Table><br />  
        </lightning:tab>
        <aura:if isTrue="{!v.is_supervisor}">
        <lightning:tab label="My Team Trainings" title="2nd tab extended title">
            <c:DT_Table listname="Training_Dashboard_My_Team_View" showTrainingDashboardButtons="true"
            showTrainingEditAction="true"></c:DT_Table><br />  
        </lightning:tab>
        </aura:if>
        <aura:if isTrue="{!v.is_moduleManager == false}">
            <lightning:tab label="Other Trainings ">
                <c:DT_Table listname="Training_Dashboard_Other_Training" showTrainingDashboardButtons="true"
                showTrainingEditAction="true"></c:DT_Table><br />        
            </lightning:tab>
        </aura:if>
        <!-- module manager can see-->
        <aura:if isTrue="{!v.is_moduleManager}">
            <lightning:tab label="All Trainings">
                <c:DT_Table listname="Training_Dashboard" showTrainingDashboardButtons="true"
                showTrainingEditAction="true"></c:DT_Table><br />        
            </lightning:tab>
        </aura:if>
            </lightning:tabset>
        
</aura:component>
