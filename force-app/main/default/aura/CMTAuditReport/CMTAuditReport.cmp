<aura:component controller="CMTAuditReportController" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
    access="global">
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <aura:attribute name="processList" type="List" />

    <div class='scrollableX'>
        <table class="slds-table slds-table_cell-buffer slds-table_bordered" aria-labelledby="element-with-table-label other-element-with-table-label">
            <thead>
                <tr class="slds-line-height_reset">
                    <th class="" scope="col">
                        <div class="slds-truncate" title="Audit Process Name">Audit Process Name</div>
                    </th>
                    <th class="" scope="col">
                        <div class="slds-truncate" title="Standard">Standard</div>
                    </th>
                    <th class="widthCss" scope="col">
                        <div class="slds-truncate" title="Clause Name">Clause Number</div>
                    </th>
                    <th class="" scope="col">
                        <div class="slds-truncate" title="Question">Audit Question</div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <aura:iteration items="{!v.processList}" var="process">
                    <tr class="slds-hint-parent">
                        <td data-label="Account Name">
                            <div class="slds-truncate" title="{!process.processName}">{!process.processName}</div>
                        </td>
                        <td data-label="Account Name">
                            <div class="slds-truncate" title="{!process.complianceName}">{!process.complianceName}</div>
                        </td>
                        <td colspan="2">
                            <table class="slds-table slds-table_cell-buffer slds-table_bordered borderStyle" aria-labelledby="element-with-table-label other-element-with-table-label">
                                <aura:iteration items="{!process.checklistList}" var="checklist">
                                    <tr>
                                        <td data-label="questionId" class='widthCss'>
                                            <div class="slds-truncate" title="{!checklist.questionId}">{!checklist.questionId}</div>
                                        </td>
                                        <td data-label="question">
                                            <div class="slds-truncate" title="{!checklist.question}">{!checklist.question}</div>
                                        </td>
                                    </tr>
                                </aura:iteration>
                            </table>
                        </td>
                    </tr>
                </aura:iteration>
            </tbody>
        </table>
    </div>
</aura:component>