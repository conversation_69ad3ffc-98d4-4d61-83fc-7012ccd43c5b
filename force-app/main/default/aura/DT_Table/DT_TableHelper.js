({
    loadColumn: function (component, event, helper, fieldName) {

    },

    checkForTaskByUrl: function (component, event, helper) {
        var url = new URL(window.location.href);

        var taskId = url.searchParams.get('c__ncId');


        var pageName = url.searchParams.get('c__pageName');

        if (pageName == 'audit') {
            setTimeout(function () {
                if (url.searchParams.get('c__audId')) {
                    component.openAuditPreviewModal(url.searchParams.get('c__audId'));
                }
            }, 2400);
        }
        else if (pageName == 'equipment') {
            setTimeout(function () {
                if (url.searchParams.get('c__woId')) {
                    component.openeditAuditChecklistModal(url.searchParams.get('c__woId'));
                }
            }, 2400);
        }

        else if (url.searchParams.get('c__etId') != undefined && url.searchParams.get('c__etId') != '' && url.searchParams.get('c__etId') != null &&  pageName == 'training' && component.get('v.listname') == 'Training_Dashboard_My_View') {
            var trainingId = url.searchParams.get('c__etId');
            setTimeout(function () {
                component.openTrainingCourseEditModal(trainingId);
            }, 2400);
        }
            else if (url.searchParams.get('c__apId') != undefined && url.searchParams.get('c__apId') != '' && url.searchParams.get('c__apId') != null && pageName == 'management-review' && component.get('v.listname') =='ActionPlan_Admin') {
                setTimeout(function () {
                    component.set('v.showActionPlanEditModal', true);
                    component.find('actionplansection').set('v.recordId', url.searchParams.get('c__apId'));
                    component.find('actionplansection').set('v.momId', "EditModeOfActionPlanLog");
                    component.find('actionplansection').showNewActionPlanEditModal();
                    component.find('actionplansection').handleLoad();
                }, 2400);
            }
        else {
            if (taskId) {
                setTimeout(function () {
                    component.openNcModal(taskId);
                }, 2400);
            }
            else if(url.searchParams.get('c__caId') && component.get('v.listname') == 'Non_Confirmity_Log') {
                taskId = url.searchParams.get('c__caId');
                if (taskId) {
                    setTimeout(function () {
                        component.openCAModal(taskId);
                    }, 2500);
                }
            }
        }

        // debugger;
        if (url.searchParams.get('c__cdocId') != undefined && url.searchParams.get('c__cdocId') != '' && url.searchParams.get('c__cdocId') != null && component.get('v.listname') == 'Document_Master_List') {
            setTimeout(function () {
                component.openDocChangeRequestModal(url.searchParams.get('c__cdocId'));
            }, 2500);
        }
    },

    refreshTable: function (component, event, helper, fieldName) {
        component.set('v.loaded', false);
        console.log('list name ' + component.get('v.listname'));
        // console.log('fieldName ' + fieldName);
        // console.log('recordID ' + component.get('v.recordId'));
        // call the apex class method and fetch Contact list  
        var action = component.get("c.loadColumnsAndDataOffset");
        if (component.get('v.listname') == 'Document_Revision_History') {
            component.set('v.sortdirectionasc', true);
        }
        action.setParams({
            'metdataName': component.get('v.listname'),
            'columnAPIName': fieldName,
            'sortdirection': component.get('v.sortdirectionasc'),
            'recordId': component.get('v.recordId'),
            'fromDate': component.get('v.fromDate'),
            'toDate': component.get('v.toDate'),
            'offset': 0,
            'pageSize': 0,
            'filters': JSON.stringify(component.get('v.filteredDataList'))
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                // set ContactList list with return value from server.
                let dataJson = response.getReturnValue();
                //console.log('dataJson ' + JSON.stringify(dataJson));
                component.set("v.dataObj", dataJson);
                component.set("v.tableData", dataJson.dataRowList);
                if (dataJson != undefined) {
                    component.set("v.tableColumns", dataJson.columnList);
                    component.set("v.showCheckbox", dataJson.showCheckbox);
                    component.set("v.allowSearch", dataJson.showSearch);
                    component.set("v.allowFilter", dataJson.isFilterable);
                    component.set("v.editable", dataJson.isEditable);
                    component.set("v.title", dataJson.title);
                    if (component.get('v.listname') == 'Minutes_of_Meeting_Admin') {
                        var meetingaction = component.get("c.getmeetingName");
                        meetingaction.setParams({ 'meetingId': component.get('v.recordId') });
                        meetingaction.setCallback(this, function (response) {
                            if (response.getState() == 'SUCCESS') {
                                dataJson.title = dataJson.title + ' (' + response.getReturnValue() + ')';
                                component.set("v.title", dataJson.title);
                            }
                        });
                        $A.enqueueAction(meetingaction);

                    } else if (component.get('v.listname') == 'Completed_Training' || component.get('v.listname') == 'My_Completed_Training_Employee') {
                        dataJson.title = dataJson.title + ' of ' + component.get('v.appendName');;
                        component.set("v.title", dataJson.title);
                    }
                    component.set("v.allDataList", dataJson.allDataList);
                    component.set("v.filteredDataList", dataJson.filters);
                    component.set("v.componentName", dataJson.componentName);

                    component.set("v.pageSize", dataJson.recordPerPage);
                    component.set("v.pageNumber", 1);
                    component.set("v.start", 0);
                    component.set("v.totalSize", dataJson.count);
                    component.set("v.totalPages", Math.ceil(dataJson.count / component.get("v.pageSize")));
                    component.set("v.end", component.get("v.totalPages") - 1);
                    if (component.get("v.totalPages") == 0) {
                        component.set("v.totalPages", 1);
                    }
                    component.set("v.offset", 0);
                    component.set("v.tableData", dataJson.dataRowList);

                    if (dataJson.objectName == 'Management_Review__c') {

                        let recordId = dataJson.allDataList[0] != undefined ? dataJson.allDataList[0].key : undefined;
                        var eventObj = {};
                        eventObj.currentTarget = {};
                        eventObj.currentTarget.id = recordId;
                        component.set("v.activeRow", recordId);

                        if (recordId != undefined) {
                            helper.showMOMTable(component, eventObj, helper);
                        }
                    } else if (dataJson.objectName == 'Document__c') {

                        let recordId = dataJson.allDataList[0] != undefined ? dataJson.allDataList[0].key : undefined;
                        var eventObj = {};
                        eventObj.currentTarget = {};
                        eventObj.currentTarget.id = recordId;
                        component.set("v.activeRow", recordId);
                          helper.showFileFromInit(component, event, helper);
                        helper.showDocTable(component, eventObj, helper);
                    }
                }
                component.set("v.selectedRows", []);
            } else {
            }
            component.set('v.loaded', true);
        });
        

        $A.enqueueAction(action);
    },

    nextPrevious: function (component, offset, pagesize, fieldName) {
        component.set('v.loaded', false);
        // call the apex class method and fetch Contact list  
        var action = component.get("c.loadColumnsAndDataOffset");
        action.setParams({
            'metdataName': component.get('v.listname'),
            'columnAPIName': fieldName,
            'sortdirection': component.get('v.sortdirectionasc'),
            'recordId': component.get('v.recordId'),
            'fromDate': component.get('v.fromDate'),
            'toDate': component.get('v.toDate'),
            'offset': offset,
            'pageSize': pagesize,
            'filters': JSON.stringify(component.get('v.filteredDataList'))
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                let dataJson = response.getReturnValue();
                component.set("v.dataObj", dataJson);
                component.set("v.tableData", dataJson.dataRowList);
                component.set("v.selectedRows", []);
                component.set("v.offset", offset);
            } else {
            }
            component.set('v.loaded', !component.get('v.loaded'));
        });
        $A.enqueueAction(action);
    },

    setCRStatusCall: function (component, event, helper, status, fieldToUpdate) {
        component.set('v.loaded', !component.get('v.loaded'));
        var action = component.get("c.setCRStatus");
        action.setParams({
            'recordIdStr': event.getSource().get("v.value"),
            'status': status,
            'fieldtoUpdate': fieldToUpdate
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() != 'false') {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Update Successful",
                        "message": "Record " + status + " successfully",
                        duration: ' 10000',
                        key: 'info_alt',
                        type: 'success',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                    helper.refreshTable(component, event, helper, 'Name');
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Update Error",
                        "message": "Record could not be updated.",
                        duration: ' 10000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', component.get('v.loaded'));
        });
        $A.enqueueAction(action);
    },

    showMOMTable: function (component, event, helper) {

        let recordIdLocal = event.currentTarget.id;
        component.set('v.recordId', recordIdLocal);
        //component.find('momtable').set('v.recordId',event.getSource().get("v.value"));
        component.find('momtable').initialize();

        component.set('v.showActionPlanTable', true);
        component.find('actionplantable').set('v.momId', recordIdLocal);
        component.find('actionplantable').set('v.managementReviewId', component.get('v.recordId'));
        component.set('v.recordId', recordIdLocal);
        component.find('actionplantable').set('v.showNewActionPlanButton', true);
        if (component.find('actionplantable').find('actionplanbutton') != undefined) {
            component.find('actionplantable').find('actionplanbutton').set('v.momId', recordIdLocal);
        }
        component.find('actionplantable').initialize();
    },

    showDocTable: function (component, event, helper) {

        let recordIdLocal = event.currentTarget.id;
        component.set('v.recordId', recordIdLocal);
        //component.find('momtable').set('v.recordId',event.getSource().get("v.value"));
        component.find('crtable').initialize();
    },

    search: function (component, nextprev) {
        component.set('v.loaded', false);
        var searchKey = component.find("searchbox").get("v.value");
        let dataList = component.get("v.allDataList");
        let pageSize = component.get("v.pageSize");
        let pageNumber = component.get("v.pageNumber");
        let offset = (pageSize * pageNumber) - pageSize;
        var paginationList = [];
        if (searchKey) {
            let dataList = component.get("v.allDataList");
            var matchList = [];
            matchList = dataList.filter(rec => JSON.stringify(rec).toLowerCase().includes(searchKey.toLowerCase()));
            if (offset < matchList.length) {
                for (let i = offset; i < matchList.length && i < offset + pageSize; i++) {
                    paginationList.push(matchList[i]);
                }
            }
            if (!nextprev) {
                component.set("v.pageNumber", 1);
                component.set("v.start", 0);
                component.set("v.totalPages", Math.ceil(matchList.length / pageSize));
            }
            component.set("v.tableData", paginationList);
        } else {
            if (!nextprev) {
                component.set("v.tableData", component.get("v.dataObj").dataRowList);
                component.set("v.pageNumber", 1);
                component.set("v.start", 0);
                component.set("v.totalPages", Math.ceil(component.get("v.dataObj").count / component.get("v.pageSize")));
            } else {
                var paginationList = [];
                if (offset < dataList.length) {
                    for (let i = offset; i < dataList.length && i < offset + pageSize; i++) {
                        paginationList.push(dataList[i]);
                    }
                    component.set("v.tableData", paginationList);
                }
            }
        }
        component.set('v.loaded', !component.get('v.loaded'));
    },

    loadComponentAttribute: function (cmp, cmpname) {
        if (cmpname == 'c:cmtWorkOrderButtons') {
            //cmp.set('v.showWorkOrderModal',true);
            cmp.handleLoad();
        } else if (cmpname == 'c:cmtAssetButtons') {
            cmp.set('v.showNewAssetButton', true);
            cmp.handleLoad();
        } else if (cmpname == 'c:cmtAuditChecklist') {
            cmp.handleLoad();
        } else if (cmpname == 'c:cmtNewOrChangeRequestForm') {
            cmp.handleLoad();
        }
    },

    showFile: function (component, event, helper) {
        try {
            var changeManagementList = ['Document_Dashboard_Is_Admin', 'Document_Dashboard_current_user_is_owner', 'Document_Dashboard_current_user_is_appro', 'Document_Dashboard_current_user_is_autho', 'Document_Dashboard_status_is_pending'];
            if (!changeManagementList.includes(component.get('v.listname'))) {
                let payload = {
                    fileId: {
                        value: event.currentTarget.id,
                        channel: 'channel',
                        source: 'Aura'
                    }
                };
                setTimeout(function () {
                    component.find('filechannel').publish(payload);
                }, 200);
            }
        } catch (error) {
        }
    },
     showFileFromInit: function (component, event, helper) {
        try {
            var changeManagementList = ['Document_Dashboard_Is_Admin', 'Document_Dashboard_current_user_is_owner', 'Document_Dashboard_current_user_is_appro', 'Document_Dashboard_current_user_is_autho', 'Document_Dashboard_status_is_pending'];
            if (!changeManagementList.includes(component.get('v.listname'))) {
                let payload = {
                    fileId: {
                        value:   component.get("v.activeRow"),
                        channel: 'channel',
                        source: 'Aura'
                    }
                };
                setTimeout(function () {
                    component.find('filechannel').publish(payload);
                }, 200);
            }
        } catch (error) {
        }
    }
})