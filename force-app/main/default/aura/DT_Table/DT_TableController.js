({
    initRecords: function (component, event, helper) {
        console.log('initRecords');
        helper.checkForTaskByUrl(component, event, helper);
        helper.refreshTable(component, event, helper, component.get('v.sortcolumn'));
    },
    
    refreshDataTable: function (component, event, helper) {
        helper.refreshTable(component, event, helper, component.get('v.sortcolumn'));
    },
    saveRecord: function (component, event, helper) {
        let selectedRow = [];
        for (let tableRow of component.get('v.tableData')) {
            if (tableRow.isSelected == true) {
                selectedRow.push(tableRow);
            }
        }
        // call the saveContact apex method for update inline edit fields update 
        component.set('v.loaded', false);
        var action = component.get("c.saveSobjectRecord");
        action.setParams({
            'rowList': selectedRow
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                component.set("v.showSaveCancelBtn", false);
                var toastEvent = $A.get("e.force:showToast");
                toastEvent.setParams({
                    "title": "Success!",
                    "message": "Record(s) updated successfully.",
                    duration: ' 5000',
                    key: 'info_alt',
                    type: 'success',
                    mode: 'pester'
                });
                toastEvent.fire();
            }
            component.set('v.loaded', !component.get('v.loaded'));
        });
        $A.enqueueAction(action);
    },

    openTraining: function (component, event, helper) {
        try {

            component.find('trainingdashboard').openEmpTrainingModal({target : {value : event.getParam('courseId'), name : event.getParam('courseName'),corseType : event.getParam('courseType')}}); 
        } catch (error) {
            console.log('error openTraining',error);
            
        }
    },

    cancel: function (component, event, helper) {
        helper.refreshTable(component, event, helper);
        component.set("v.showSaveCancelBtn", false);
    },

    onSelect: function (component, event, helper) {
        let selectedRowId = [];
        for (let tableRow of component.get('v.tableData')) {
            if (tableRow.isSelected == true) {
                selectedRowId.push(tableRow.key);
            }
        }
        component.set('v.selectedRows', selectedRowId);
    },

    sort: function (component, event, helper) {
        
        if (component.get('v.sortcolumn') == event.getSource().get('v.value')) {
            component.set('v.sortdirectionasc', !component.get('v.sortdirectionasc'));
        } else {
            component.set('v.sortdirectionasc', true);
            component.set('v.sortcolumn', event.getSource().get('v.value'));
        }
        helper.refreshTable(component, event, helper, event.getSource().get('v.value'));
    },

    search: function (component, event, helper) {
        helper.search(component, false);
    },

    nextPage: function (component, event, helper) {
        let pageNumber = component.get("v.pageNumber");
        component.set("v.pageNumber", pageNumber + 1);

        let pageSize = component.get('v.pageSize');

        let fullTable = component.get("v.dataObj");

        //component.set("v.tableData", fullTable.dataRowList.slice( (pageSize*pageNumber),(pageSize*(pageNumber+1)) ));

        component.set("v.start", pageNumber);
        let offset = (pageSize * pageNumber);
        helper.nextPrevious(component, offset, pageSize, component.get('v.sortcolumn'));
        //helper.search(component,true);

    },

    previousPage: function (component, event, helper) {
        let pageNumber = component.get("v.pageNumber");
        component.set("v.pageNumber", pageNumber - 1);

        let pageSize = component.get('v.pageSize');

        let fullTable = component.get("v.dataObj");

        //component.set("v.tableData", fullTable.dataRowList.slice( (pageSize*(pageNumber-2)),(pageSize*(pageNumber-1)) ));
        component.set("v.start", pageNumber - 2);
        let offset = (pageSize * (pageNumber - 2));
        helper.nextPrevious(component, offset, pageSize, component.get('v.sortcolumn'));
        //helper.search(component,true);
    },

    selectAll: function (component, event, helper) {
        let value = event.getSource().get('v.checked');
        let selectedRowId = [];
        let row = component.get('v.tableData')
        for (let tableRow of row) {
            tableRow.isSelected = value;
            if (value == true) {
                selectedRowId.push(tableRow.key);
            }
        }
        component.set('v.tableData', row);
        component.set('v.selectedRows', selectedRowId);
    },

    setActiveRow: function (component, event, helper) {
        let value = event.currentTarget.id;
        component.set("v.activeRow", value);

        const recordId = value;
        const componentName = component.get('v.componentName');

        if (componentName != undefined && componentName != '') {
            $A.createComponent(
                componentName, { recordId: recordId },
                function (lwcCmp, status, errorMessage) {
                    if (status === "SUCCESS") {
                        //var placeholder = component.find('placeholder');
                        component.set("v.cbody", lwcCmp);
                        /*
                        var body = component.get("v.body");
                        body.push(lwcCmp);
                        component.set("v.body", body);
                        */
                        helper.loadComponentAttribute(lwcCmp, componentName);
                    }
                    else if (status === "INCOMPLETE") {
                    }
                    else if (status === "ERROR") {
                    }
                }
            );
        } else if (component.get('v.dataObj').objectName == 'Management_Review__c') {
            helper.showMOMTable(component, event, helper);
        } else if (component.get('v.dataObj').objectName == 'Document__c') {
            helper.showFile(component, event, helper);
            helper.showDocTable(component, event, helper);
        }
        else if (component.get('v.dataObj').objectName == 'Document_Change_Request__c') {
            helper.showFile(component, event, helper);
        }

    },

    actionHandler: function (component, event, helper) {
        component.set('v.showCRModal', false);
        component.set('v.showMaintenanceModal', false);
        component.set('v.showAdditionalAssetModal', false);
        helper.refreshTable(component, event, helper, component.get('v.sortcolumn'));
        component.set('v.loaded', false);
    },

    hideConfirmationModal: function (component, event, helper) {
        component.set('v.showConfirmationModal', false);
    },
    makeMeetingComplete: function (component, event, helper) {
        component.set('v.loaded', !component.get('v.loaded'));
        component.set('v.showAddMeetingButton', false);
        var action = component.get("c.updateMeetingStatus");
        action.setParams({
            'recordIdStr': component.get('v.recordId')
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set("v.showConfirmationModal", false);
                    helper.refreshTable(component, event, helper, component.get('v.sortcolumn'));
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Success!",
                        "message": "Meeting Completed.",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'success',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                } else {
                    component.set("v.showDeleteModal", true);
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Update Failed!",
                        "message": 'Error occurred: ' + response.getReturnValue(),
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                    component.set('v.loaded', true);
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);


    },
    showDeleteModal: function (component, event, helper) {
        component.set('v.showDeleteModal', true);
        component.set('v.recordToDelete', event.getSource().get("v.value"));
    },

    hideDeleteModal: function (component, event, helper) {
        component.set('v.showDeleteModal', false);
    },

    deleteRecordCall: function (component, event, helper) {
        // call the saveContact apex method for update inline edit fields update 
        component.set('v.loaded', !component.get('v.loaded'));
        var action = component.get("c.deleteRecord");
        action.setParams({
            'recordIdStr': component.get('v.recordToDelete')
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set("v.showDeleteModal", false);
                    helper.refreshTable(component, event, helper, component.get('v.sortcolumn'));
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Success!",
                        "message": "Record deleted successfully.",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'success',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                } else {
                    component.set("v.showDeleteModal", true);
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Delete Failed!",
                        "message": 'Error occurred: ' + response.getReturnValue(),
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                    component.set('v.loaded', true);
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },

    downloadFileCall: function (component, event, helper) {
        // call the saveContact apex method for update inline edit fields update 
        component.set('v.loaded', !component.get('v.loaded'));
        var action = component.get("c.getDownloadUrl");
        action.setParams({
            'givenRecordId': event.getSource().get("v.value")
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() != 'false') {
                    let link = document.createElement("a");
                    link.setAttribute('download', '');
                    link.setAttribute('href', response.getReturnValue());
                    link.click();
                    
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Download Error",
                        "message": "Record not found as document may not be uploaded.",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', !component.get('v.loaded'));
        });
        $A.enqueueAction(action);
    },

    showFilterSection: function (component, event, helper) {
        let filterClass = component.get('v.filterClass');
        if (filterClass == 'slds-col slds-size_3-of-12') {
            component.set('v.filterClass', 'slds-col hidden');
        } else {
            component.set('v.filterClass', 'slds-col slds-size_3-of-12')
        }
    },

    setCRStatusCallApprove: function (component, event, helper) {
        helper.setCRStatusCall(component, event, helper, 'Approved', 'Status__c');
    },

    setCRStatusCallReject: function (component, event, helper) {
        helper.setCRStatusCall(component, event, helper, 'Rejected', 'Status__c');
    },

    setCRAuthorStatusCallApprove: function (component, event, helper) {
        helper.setCRStatusCall(component, event, helper, 'Approved', 'Author_Status__c');
    },

    setCRAuthorStatusCallReject: function (component, event, helper) {
        helper.setCRStatusCall(component, event, helper, 'Rejected', 'Author_Status__c');
    },

    setPublishDoc: function (component, event, helper) {
        helper.setCRStatusCall(component, event, helper, 'Published', 'Document_Status__c');
    },

    setDraftDoc: function (component, event, helper) {
        helper.setCRStatusCall(component, event, helper, 'Draft', 'Document_Status__c');
    },

    setTrainingApprove: function (component, event, helper) {
        helper.setCRStatusCall(component, event, helper, 'Approved', 'Training_Status__c');
    },

    setTrainingReject: function (component, event, helper) {
        helper.setCRStatusCall(component, event, helper, 'Pending Approval', 'Training_Status__c');
    },

    additionalParameters: function (component, event, helper) {
        component.set('v.showAdditionalAssetModal', true);
        component.find('additonalAssetParam').set('v.recordId', event.getSource().get("v.value"));
        //component.find('assetSchedule').set('v.viewType','crdocexisting');
        //component.find('assetSchedule').handleLoad();
    },

    showMOMTable: function (component, event, helper) {
        helper.showMOMTable(component, event, helper);
    },

    previewRecord: function (component, event, helper) {
    },

    showAssetComponent: function (component, event, helper) {
        component.find('assetview').set('v.recordId', event.getSource().get("v.value"));
        component.find('assetview').showAssetDetailModal();
        component.find('assetview').handleLoad();
    },

    refreshActionPlan: function (component, event, helper) {
        component.find('meetingsection').find('actionplantable').initialize();
    },

    applyFilter: function (component, event, helper) {
        try {
            helper.refreshTable(component, event, helper, component.get('v.sortcolumn'));
        } catch (e) {
        }
    },

    lookupRecord: function (component, event, helper) {
        var selectedId = event.getParam('selectedRecord');
        var index = event.getParam('index');
        var lst = component.get("v.filteredDataList");
        if (selectedId != undefined) {
            lst[index].value = selectedId.Id;
        } else {
            lst[index].value = '';
        }
        component.set("v.filteredDataList", lst);
    },

    /*********************************************Modal Display Mrthods */
    showMasterDocumentModal: function (component, event, helper) {
        //debugger;
        var crId;
        var params = event.getParam('arguments');

        if (params) {
            crId = params.taskId;
        }
        else {
            crId = event.getSource().get("v.value");
        }
        component.set('v.sectionNamePreview', 'editDoc');
        component.set('v.showCRModal', true);
        component.find('crcmp').set('v.recordId', crId);
        component.find('crcmp').set('v.viewType', 'maindoc');
        component.set('v.documentHeader', 'Edit Document Properties');
        component.find('crcmp').handleLoad();
    },
    showCRDocumentModal: function (component, event, helper) {
        var crId;
        var params = event.getParam('arguments');

        if (params) {
            crId = params.taskId;
        }
        else {
            crId = event.getSource().get("v.value");
        }
        component.set('v.sectionNamePreview', 'editCR');
        component.set('v.showCRModal', true);
        component.find('crcmp').set('v.recordId', crId);
        component.find('crcmp').set('v.viewType', 'crdoc');
        component.set('v.documentHeader', 'Edit Document Change Request');
        component.find('crcmp').handleLoad();
    },
    showNewCRDocumentModal: function (component, event, helper) {
        component.set('v.sectionNamePreview', 'newDoc');
        component.set('v.showCRModal', true);
        //component.find('crcmp').set('v.recordId',event.getSource().get("v.value"));
        component.find('crcmp').set('v.viewType', 'crdocnew');
        component.set('v.documentHeader', 'Add New Document');
        component.find('crcmp').handleLoad();
    },
    showMaintenanceModal: function (component, event, helper) {
        component.set('v.showMaintenanceModal', true);
        component.find('assetSchedule').set('v.recordId', event.getSource().get("v.value"));
        //component.find('assetSchedule').set('v.viewType','crdocexisting');
        //component.find('assetSchedule').handleLoad();
    },
    showCRModal: function (component, event, helper) {
        var crId;
        var params = event.getParam('arguments');

        if (params) {
            crId = params.taskId;
        }
        else {
            crId = event.getSource().get("v.value");
        }
        component.set('v.sectionNamePreview', 'newCR');
        component.set('v.showCRModal', true);
        component.find('crcmp').set('v.recordId', crId);
        component.find('crcmp').set('v.viewType', 'crdocexisting');
        component.set('v.documentHeader', 'Document Change Request');
        component.find('crcmp').handleLoad();
    },
    hideCRModal: function (component, event, helper) {
        component.set('v.showCRModal', false);
    },
    previewAuditChecklistModal: function (component, event, helper) {
        component.set('v.showAuditChecklistModal', true);
        component.find('auditChecklist').set('v.recordId', event.getSource().get("v.value"));
        component.find('auditChecklist').set('v.isPreviewMode', true);
        component.find('auditChecklist').handleLoad();
    },
    editAuditChecklistModal: function (component, event, helper) {
        var recId;
        var params = event.getParam('arguments');

        if (params) {
            recId = params.recId;
        }
        else {
            recId = event.getSource().get("v.value");
        }
        var action = component.get("c.checkInternalAuditAccess");
        action.setParams({
            'recordId': recId
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    try {
                        component.set('v.showAuditChecklistModal', true);
                        component.find('auditChecklist').set('v.recordId', recId);
                        component.find('auditChecklist').set('v.showModal', true);
                        component.find('auditChecklist').handleLoad();
                    } catch (e) {
                    }
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);

    },
    previewAudit: function (component, event, helper) {
        let ids = [];
        var params = event.getParam('arguments');

        if (params) {
        ids.push(params.taskId);
        }
        else {
            ids.push(event.getSource().get("v.value"));
        }
        
            var test= component.find("auditschedulebtn");
            component.find("auditschedulebtn").set('v.selectedRecordIds', ids);
            component.find("auditschedulebtn").openAuditPreviewModal();
        
    },
    /************************Asset Managemet / WO Model Methods */
    previewWOModal: function (component, event, helper) {
        component.set('v.showWOEditModal', true);
        component.find('workordersection').set('v.recordId', event.getSource().get("v.value"));
        component.find('workordersection').set('v.showWorkOrderButton', false);
        component.find('workordersection').set('v.showWorkOrderModal', false);
        component.find('workordersection').set('v.previewWOModal', true);
        component.find('workordersection').handleLoad();
    },
    showWOEditModal: function (component, event, helper) {
        component.set('v.showWOEditModal', true);
        component.find('workordersection').set('v.recordId', event.getSource().get("v.value"));
        component.find('workordersection').set('v.showWorkOrderButton', false);
        component.find('workordersection').set('v.showWorkOrderModal', true);
        component.find('workordersection').handleLoad();
        //component.find('assetSchedule').set('v.viewType','crdocexisting');
        //component.find('assetSchedule').handleLoad();
    },
    showWODeleteModal: function (component, event, helper) {


        var action = component.get("c.checkWOAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set('v.showDeleteModal', true);
                    component.set('v.recordToDelete', event.getSource().get("v.value"));
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    /************************Asset Managemet / WO Model Methods */
    showAssetEditModal: function (component, event, helper) {
        component.set('v.showAssetEditModal', true);
        component.find('assetsection').set('v.recordId', event.getSource().get("v.value"));
        component.find('assetsection').set('v.showNewAssetButton', true);
        component.find('assetsection').set('v.showAssetButton', false);
        //component.find('assetSchedule').set('v.viewType','crdocexisting');
        component.find('assetsection').handleLoad();
    },
    previewAssetModal: function (component, event, helper) {
        component.set('v.showAssetEditModal', true);
        component.find('assetsection').set('v.recordId', event.getSource().get("v.value"));
        component.find('assetsection').set('v.showAssetPreviewModal', true);
        component.find('assetsection').set('v.showAssetButton', false);
        component.find('assetsection').handleLoad();
    },
    showMeetingEditModal: function (component, event, helper) {

        var action = component.get("c.checkMeetingAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    try {
                        component.set('v.showMeetingEditModal', true);
                        component.find('meetingsection').set('v.recordId', event.getSource().get("v.value"));
                        //component.find('meetingsection').set('v.showMeetingButton',false);
                        component.find('meetingsection').handleClick();
                        component.find('meetingsection').handleLoad();
                        component.find('meetingsection').handleClick();
                    } catch (e) {
                    }
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);




    },
    previewMeetingModal: function (component, event, helper) {
        component.set('v.showMeetingEditModal', true);
        component.find('meetingsection').set('v.recordId', event.getSource().get("v.value"));
        //component.find('meetingsection').set('v.showMeetingButton',false);
        component.find('meetingsection').handleClick();
        component.find('meetingsection').handleLoad();
        component.find('meetingsection').set('v.showPreviewMeetingModal', true);

    },
    showAgendaEditModal: function (component, event, helper) {
        var action = component.get("c.checkMeetingMinutesAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    try {
                        component.set('v.showAgendaEditModal', true);
                        component.find('agendasection').set('v.recordId', event.getSource().get("v.value"));
                        component.find('agendasection').showAgendaModal();
                        component.find('agendasection').set('v.showAgendaButton', false);
                        component.find('agendasection').handleLoad();
                    } catch (e) {
                    }
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);

    },
    previewAgendaModal: function (component, event, helper) {
        component.set('v.showAgendaEditModal', true);
        component.find('agendasection').set('v.recordId', event.getSource().get("v.value"));
        component.find('agendasection').set('v.showAgendaButton', false);
        component.find('agendasection').set('v.showAgendaPreviewModal', true);
        component.find('agendasection').handleLoad();
    },
    showActionPlanEditModal: function (component, event, helper) {
        component.set('v.showActionPlanEditModal', true);
        //component.find('actionplansection').set('v.momId',event.getSource().get("v.value"));
        component.find('actionplansection').set('v.recordId', event.getSource().get("v.value"));
        component.find('actionplansection').set('v.momId', component.get("v.momId"));
        component.find('actionplansection').showNewActionPlanEditModal();
        component.find('actionplansection').handleLoad();
    },
    showActionPlanLogEditModal: function (component, event, helper) {
        component.set('v.showActionPlanEditModal', true);
        component.find('actionplansection').set('v.recordId', event.getSource().get("v.value"));
        component.find('actionplansection').set('v.momId', "EditModeOfActionPlanLog");
        component.find('actionplansection').showNewActionPlanEditModal();
        component.find('actionplansection').handleLoad();
    },
    showActionPlanNewModal: function (component, event, helper) {
        component.set('v.showActionPlanEditModal', true);
        component.find('actionplansection').set('v.momId', event.getSource().get("v.value"));
        component.find('actionplansection').showNewActionPlanEditModal();
        component.find('actionplansection').handleLoad();
    },
    showAuditSetupModal: function (component, event, helper) {
        let ids = [];
        ids.push(event.getSource().get("v.value"));
        component.find('auditschedulebtn').set('v.selectedRecordIds', ids);
        component.find('auditschedulebtn').showModal();
    },
    showMeetingDeleteModal: function (component, event, helper) {


        var action = component.get("c.checkMeetingAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set('v.showDeleteModal', true);
                    component.set('v.recordToDelete', event.getSource().get("v.value"));
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    showActionPlanDeleteModal: function (component, event, helper) {

        var action = component.get("c.checkActionPlanAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set('v.showDeleteModal', true);
                    component.set('v.recordToDelete', event.getSource().get("v.value"));
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    showNCDeleteModal: function (component, event, helper) {


        var action = component.get("c.checkNCAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set('v.showDeleteModal', true);
                    component.set('v.recordToDelete', event.getSource().get("v.value"));
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    showAuditDeleteModal: function (component, event, helper) {


        var action = component.get("c.checkAuditAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set('v.showDeleteModal', true);
                    component.set('v.recordToDelete', event.getSource().get("v.value"));
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    showDocDeleteModal: function (component, event, helper) {


        var action = component.get("c.checkDocAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set('v.showDeleteModal', true);
                    component.set('v.recordToDelete', event.getSource().get("v.value"));
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    showConfirmationModal: function (component, event, helper) {

        var action = component.get("c.checkMeetingAccess");
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set('v.showConfirmationModal', true);
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    showActionPlanTable: function (component, event, helper) {
        component.set('v.showActionPlanTable', true);
        component.find('actionplantable').set('v.momId', event.getSource().get("v.value"));
        component.find('actionplantable').set('v.managementReviewId', component.get('v.recordId'));
        component.set('v.recordId', event.getSource().get("v.value"));
        component.find('actionplantable').set('v.showNewActionPlanButton', true);
        if (component.find('actionplantable').find('actionplanbutton') != undefined) {
            component.find('actionplantable').find('actionplanbutton').set('v.momId', event.getSource().get("v.value"));
        }
        component.find('actionplantable').initialize();
    },
    previewNCModal: function (component, event, helper) {
        component.set('v.showAgendaEditModal', true);
        component.find('ncsection').set('v.recordId', event.getSource().get("v.value"));
        component.find('ncsection').previewNC();
    },
    showNCEditModal: function (component, event, helper) {
        var ncId;
        var params = event.getParam('arguments');
        if (params) {
            ncId = params.taskId;
        }
        else {
            ncId = event.getSource().get("v.value");
        }
        var action = component.get("c.checkNCEditAccess");
        action.setParams({
            'recordId': ncId
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    try {
                        component.set('v.showAgendaEditModal', true);
                        component.find('ncsection').set('v.recordId', ncId);
                        component.find('ncsection').showNCEditModalFunction();
                        component.find('ncsection').handleLoad();
                    } catch (e) {
                    }
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    OpenCAPreviewModal: function (component, event, helper) {

        //get method paramaters
        var CA_id = '';
        var params = event.getParam('arguments');

        if (params) {
            CA_id = params.CA_id;
        }
        component.set('v.showCAPreviewMode', true);
        component.find('casectionforPreview').set('v.recordId', CA_id);
        component.find('casectionforPreview').set('v.fromcmp', true);
        component.find('casectionforPreview').openCAPreviewModal();
    },
    showCAEditModal: function (component, event, helper) {
        var caId;
        var params = event.getParam('arguments');

        if (params) {
            caId = params.taskId;
        }
        else {
            caId = event.getSource().get("v.value");
        }

        var action = component.get("c.checkCAEditAccess");
        action.setParams({
            'recordId': caId
        });
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    try {
                        component.set('v.showAgendaEditModal', true);
                        component.find('casection').set('v.recordId', caId);
                        // component.find('casection').set('v.recordId', event.getSource().get("v.value"));
                        component.find('casection').showCAEditModalFunction();
                    } catch (e) {
                    }
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);

    },
    showCAPreviewModal: function (component, event, helper) {
        component.set('v.showAgendaEditModal', true);
        component.find('casection').set('v.recordId', event.getSource().get("v.value"));
        component.find('casection').openCAPreviewModal();
    },
    showCADeleteModal: function (component, event, helper) {


        var action = component.get("c.checkCAAccess");
        action.setParams({ CAId : event.getSource().get("v.value")});
        action.setCallback(this, function (response) {
            if (response.getState() === "SUCCESS") {
                if (response.getReturnValue() == 'true') {
                    component.set('v.showDeleteModal', true);
                    component.set('v.recordToDelete', event.getSource().get("v.value"));
                } else {
                    var toastEvent = $A.get("e.force:showToast");
                    toastEvent.setParams({
                        "title": "Error!",
                        "message": "You don't have access to perform this action",
                        duration: ' 5000',
                        key: 'info_alt',
                        type: 'error',
                        mode: 'pester'
                    });
                    toastEvent.fire();
                }
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
    showInviteModal: function (component, event, helper) {
        try {
            component.set('v.showInvite', true);
            component.find('invite').showModalFunction();
            component.find('invite').set('v.recordId', event.getSource().get("v.value"));
            component.find('invite').handleLoad();
        } catch (e) {
        }
    },
    showTrainingCourseEditModal: function (component, event, helper) {
        try {

            var caId;
            var params = event.getParam('arguments');

            if (params) {
                caId = params.taskId;
            }
            else {
                caId = event.getSource().get("v.value");
            }
            component.set('v.loaded', false);
            var action = component.get("c.checkCourseAccess");
            action.setParams({ 'recordId': caId });
            action.setCallback(this, function (response) {
                if (response.getState() === "SUCCESS") {
                    if (response.getReturnValue() == 'true') {
                        component.find('TrainingCourseModal').set('v.recordId', event.getSource().get("v.value"));
                        component.find('TrainingCourseModal').set('v.showCourseModal', true);
                        component.find('TrainingCourseModal').handleLoad();
                    } else {
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Error!",
                            "message": "You don't have access to perform this action",
                            duration: ' 5000',
                            key: 'info_alt',
                            type: 'error',
                            mode: 'pester'
                        });
                        toastEvent.fire();
                    }
                }
                component.set('v.loaded', true);
            });
            $A.enqueueAction(action);
        } catch (e) {
            var toastEvent = $A.get("e.force:showToast");
            toastEvent.setParams({
                "title": "Error!",
                "message": "Something went wrong, Please contact the administrator!",
                duration: ' 5000',
                key: 'info_alt',
                type: 'error',
                mode: 'pester'
            });
            toastEvent.fire();
            component.set('v.loaded', true);
        }
    },
    showTrainingCoursePreviewModal: function (component, event, helper) {
        try {
            component.set('v.loaded', false);
            component.find('TrainingCourseModal').set('v.recordId', event.getSource().get("v.value"));
            //component.find('TrainingCourseModal').set('v.readOnlyMode');
            component.find('TrainingCourseModal').set('v.readOnlyMode', true);
            component.find('TrainingCourseModal').set('v.showCoursePreviewModal', true);
            component.find('TrainingCourseModal').handleLoad();
            component.set('v.loaded', true);
        } catch (e) {
            var toastEvent = $A.get("e.force:showToast");
            toastEvent.setParams({
                "title": "Error!",
                "message": "Something went wrong, Please contact the administrator!",
                duration: ' 5000',
                key: 'info_alt',
                type: 'error',
                mode: 'pester'
            });
            toastEvent.fire();
            component.set('v.loaded', true);
        }
    },
    showTrainingCoursesDeleteModal: function (component, event, helper) {
        try {
            component.set('v.loaded', false);
            var action = component.get("c.checkCourseAccess");
            action.setCallback(this, function (response) {
                if (response.getState() === "SUCCESS") {
                    if (response.getReturnValue() == 'true') {
                        component.set('v.showDeleteModal', true);
                        component.set('v.recordToDelete', event.getSource().get("v.value"));
                    } else {
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Error!",
                            "message": "You don't have access to perform this action",
                            duration: ' 5000',
                            key: 'info_alt',
                            type: 'error',
                            mode: 'pester'
                        });
                        toastEvent.fire();
                    }
                }
                component.set('v.loaded', true);
            });
            $A.enqueueAction(action);
        } catch (e) {
            var toastEvent = $A.get("e.force:showToast");
            toastEvent.setParams({
                "title": "Error!",
                "message": "Something went wrong, Please contact the administrator!",
                duration: ' 5000',
                key: 'info_alt',
                type: 'error',
                mode: 'pester'
            });
            toastEvent.fire();
            component.set('v.loaded', true);
        }
    },
    showTrainingCourseDeleteModal: function (component, event, helper) {
        try {
            component.set('v.loaded', false);
            var action = component.get("c.checkCourseAccess");
            action.setCallback(this, function (response) {
                if (response.getState() === "SUCCESS") {
                    if (response.getReturnValue() == 'true') {
                        component.set('v.showDeleteModal', true);
                        component.set('v.recordToDelete', event.getSource().get("v.value"));
                    } else {
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Error!",
                            "message": "You don't have access to perform this action",
                            duration: ' 5000',
                            key: 'info_alt',
                            type: 'error',
                            mode: 'pester'
                        });
                        toastEvent.fire();
                    }
                }
                component.set('v.loaded', true);
            });
            $A.enqueueAction(action);
        } catch (e) {
            component.set('v.loaded', true);
            var toastEvent = $A.get("e.force:showToast");
            toastEvent.setParams({
                "title": "Error!",
                "message": "Something went wrong, Please contact the administrator!",
                duration: ' 5000',
                key: 'info_alt',
                type: 'error',
                mode: 'pester'
            });
            toastEvent.fire();
        }
    },
  
    showTrainingDeleteModal: function (component, event, helper) {
        try {
            var action = component.get("c.checkTrainingAccess");
            action.setCallback(this, function (response) {
                if (response.getState() === "SUCCESS") {
                    if (response.getReturnValue() == 'true') {
                        component.set('v.showDeleteModal', true);
                        component.set('v.recordToDelete', event.getSource().get("v.value"));
                    } else {
                        var toastEvent = $A.get("e.force:showToast");
                        toastEvent.setParams({
                            "title": "Error!",
                            "message": "You don't have access to perform this action",
                            duration: ' 5000',
                            key: 'info_alt',
                            type: 'error',
                            mode: 'pester'
                        });
                        toastEvent.fire();
                    }
                }
                component.set('v.loaded', true);
            });
            $A.enqueueAction(action);
        } catch (e) {
        }
    },
    showTrainingPreviewModal: function (component, event, helper) {
        try {
                var action = component.get("c.getTraining");
                action.setParams({
                    'recordId': event.getSource().get("v.value")
                });
                action.setCallback(this, function (response) {
                    if (response.getState() === "SUCCESS") {
                    component.set('v.showEmployeeTrainingEdit', true);
                    component.find('employeetraining').set('v.viewMode',true);
                    component.find('employeetraining').set('v.recordId',  response.getReturnValue());
                    component.find('employeetraining').showModalFunction();

                    }
                    component.set('v.loaded', true);
                });
                $A.enqueueAction(action);
        } catch (e) {
        }
    },
    showEmployeeTrainingEditModal: function (component, event, helper) {

        try {
            var trainingId;
            var gotIdFromUrl = false;
            var params = event.getParam('arguments');

            if (params) {
                trainingId = params.recId;
                gotIdFromUrl = true;
            }
            else {
                trainingId = event.getSource().get("v.value");
            }
            var action = component.get("c.checkTrainingAccess");   
            action.setParams({
                'recordId': trainingId
            });
            action.setCallback(this, function (response) {
                if (response.getState() === "SUCCESS") {
                    if (response.getReturnValue().isAccesible == 'true') {
                        let trainingIdLocal;
                        var params = event.getParam('arguments');
            
                        if (params) {
                            trainingIdLocal = params.recId;
                        }
                        else {
                            trainingIdLocal = response.getReturnValue().trainingId;
                        }
                        component.set('v.showEmployeeTrainingEdit', true);
                        component.find('employeetraining').set('v.recordId', trainingIdLocal);
                        setTimeout(function () {
                        component.find('employeetraining').showModalFunction();
                        }, 2400);
                    } else {
                        if(gotIdFromUrl === true) {
                            component.set('v.showEmployeeTrainingEdit', true);
                            component.find('employeetraining').set('v.viewMode',true);
                            component.find('employeetraining').set('v.recordId', trainingId);
                            setTimeout(function () {
                                component.find('employeetraining').showModalFunction();
                            }, 2400);
                        } else {
                            var toastEvent = $A.get("e.force:showToast");
                            toastEvent.setParams({
                                "title": "Error!",
                                "message": "You don't have access to perform this action",
                                duration: ' 5000',
                                key: 'info_alt',
                                type: 'error',
                                mode: 'pester'
                            });
                            toastEvent.fire();
                        }
                    }
                }
                component.set('v.loaded', true);
            });
            $A.enqueueAction(action);
        } catch (e) {
        }
    },
    /*********************************************Modal Display Mrthods */
})