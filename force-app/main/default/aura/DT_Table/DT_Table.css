.THIS .slds-table_cell-buffer tr>td:first-child {
    padding-left: 0px;
}

.THIS .slds-card__body,
.THIS .slds-card__header {
    margin-top: 0px;
    margin-bottom: 0px;
    padding: 0px;
    padding-top: 0rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-bottom: 0.75rem;
}

.THIS .maincontent .slds-card,
.THIS .filtermode {
    border: none;
    box-shadow: 0px 0px 5px 1px #ccc;
}

.THIS .dttablemain .slds-modal__content {
    background-color: #f6f6f6;
}

.THIS .dttablemain .slds-modal__header {
    background-color: #d2d2d2;
}

.THIS .dttablemain button.section-control.slds-button.slds-button_reset.slds-accordion__summary-action,
.THIS .dttablemain button.section-control.slds-button.slds-button_reset.slds-accordion__summary-action,
.THIS .slds-accordion__summary-action {
    /*background-color: #d2d2d2 !important ;*/
    color: #474747 !important;
    padding: 0.5rem 0.25rem 0.5rem 0.5rem;
    margin-right: 0px;
    margin-bottom: 0.1rem;
    margin-top: 0.1rem;
}

.THIS .dttablemain .slds-accordion__list-item {
    border-top-style: none;
}

.THIS .slds-table thead th,
.THIS .slds-is-sortable .slds-th__action:hover,
.THIS .slds-is-sortable .slds-th__action:focus {
    background-color: rgb(255, 255, 255);
    padding-left: var(--lwc-tableCellSpacing, 0.5rem);
    padding-right: var(--lwc-tableCellSpacing, 0.5rem);
    font-weight: var(--lwc-fontWeightBold, 700);
    line-height: normal;
    color: #1a1a1a;
}

.THIS .slds-accordion__summary {
    background-color: transparent !important;
}

.THIS button.section-control.slds-button.slds-button_reset.slds-accordion__summary-action {
    background-color: #d2d2d2;
}

.THIS .slds-combobox__form-element.slds-input-has-icon.slds-input-has-icon_right button {
    background-color: #ffffff;
    color: #333333;
    border-color: #d2d2d2;
}

.THIS .slds-combobox__form-element.slds-input-has-icon.slds-input-has-icon_right button:disabled {
    background-color: rgb(243, 243, 243);
    color: rgb(116, 116, 116);
    border-color: rgb(201, 201, 201);
    user-select: none;
    cursor: not-allowed;
}

.THIS .slds-table thead th {
    border-bottom: solid 2px #cfcfcf;
}

.THIS .slds-table td tr {
    border: solid 1px #6a759b;
}

.THIS th .slds-has-flexi-truncate span.slds-truncate {
    width: 95%;
    /* text-align: center; */
}

.THIS .slds-table {
    color: black;
}

/* .THIS .slds-table tbody tr:nth-child(odd) { 
    background-color: rgb(241 244 255);
}  */

.THIS .active-row {
    color: black !important;
    border-radius: 3px !important;
    background-color: #eaf7fd !important
}

.THIS .inactive-row {
    color: black !important;
    border-top: solid 1px #d9d9d9;
    cursor: pointer;
}

.THIS .slds-table tr td:first-child {
    max-width: 3rem;
    width: 2.5rem;
    padding-left: 0.5rem;
}

.THIS .slds-table tr td {
    max-width: 4rem;
    width: 2.5rem;
    /* text-align: center; */
}

.THIS .slds-table tr:last-child {
    border-bottom: solid 1px #e6e6e6;
}

.THIS .slds-table tbody span.slds-truncate,
.THIS .slds-table thead div.slds-truncate,
.THIS lightning-formatted-number {
    width: -webkit-fill-available;
    /* text-align: center; */
}

.THIS {
    border: none
}

.THIS .table-wrap {
    display: block;
    width: 100%;
    overflow: auto;
}

.THIS .dt-table {
    overflow-x: auto;
    width: 100%;
    /*display: block;*/
}

.THIS .action {
    /*line-height: 2.85em*/
    padding-top: 0.25rem;
}

.THIS .searchaction {
    line-height: 2.85em;
    margin-right: .51rem;
    position: relative;
    z-index: 0;
}

.THIS .filteraction {
    line-height: 2.85em;
    margin-left: .3rem;
    margin-top: 2px;
}

.THIS .cDT_Table button.slds-button.slds-th__action.slds-text-link_reset {
    width: 100%;
    text-align: left;
    padding: 0px;
}

.THIS .searchaction button:hover {
    color: rgb(104, 185, 226);
}

.THIS .width-10em {
    max-width: 10em;
}

.THIS .width-12em {
    max-width: 12.5em;
}

.THIS .width-14em {
    max-width: 14em;
}

.THIS .width-16em {
    max-width: 16em;
}

.THIS .width-5em {
    max-width: 5.2em;
}

.THIS .action button,
.THIS .slds-file-selector__button {
    background-color: #253d87;
    border-color: #253d87;
    color: white;
    cursor: pointer;
}

.THIS .action button:disabled,
.THIS .slds-file-selector__button:disabled {
    color: #888;
    border-color: #d8d6d6;
    background-color: #d8d6d6;
    cursor: normal;
}

.THIS button:hover,
.THIS button:focus {
    color: rgb(104, 185, 226);
    cursor: pointer;
}

.THIS button .slds-button_icon {
    color: rgb(37 60 134) !important;
}

.THIS .icon-style {
    background: #f2f5f6 !important;
    border-radius: 50% !important;
    height: 28px !important;
    width: 28px !important;
    --slds-c-icon-color-foreground: #9aa2aa;
}

.THIS .slds-file-selector__text {
    /*display:none;*/
}

.THIS .slds-file-selector__dropzone {
    border: none;
}

.THIS lightning-input.slds-form-element {
    width: 100%;
    /*text-align: center;*/
}

.THIS button.slds-button.slds-th__action.slds-text-link_reset {
    width: 100%;
}

.THIS th svg.slds-icon.slds-icon-text-default.slds-icon_xx-small {
    fill: black;
    ;
}

.THIS span.slds-icon_container.slds-icon-utility-arrowup,
.THIS span.slds-icon_container.slds-icon-utility-arrowdown {
    width: 2px;
}

.THIS .pagination_label {
    line-height: 3em;
    max-width: 5em;
    text-align: center;
    margin: 0em .5em 0em .5em;
}

.THIS .pagination_button {
    max-width: 2.7em;
    text-align: right;
}

.THIS .hidden {
    display: none;
}

.THIS .toolbar {
    padding-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding-bottom: 0.75rem;
    border-bottom: solid 1px #eee;
}

.THIS .title {
    line-height: 2.3rem;
    font-size: 1rem;
    font-weight: 600;
    padding-left: 0.5rem;
    color: #2c2c2c;
}

.THIS .toolbar-items button {
    text-align: right;
    margin-right: 0.5rem;
}

.THIS .checkboxesdsgn.internalAuditChecklistCheckCss .slds-radio [type=radio]+.slds-radio__label .slds-radio_faux {
    margin-right: 0.5rem !important;
}

.THIS .filtermode {
    height: 100%;
    margin-left: 1rem;
    border-radius: 0.25rem;
    margin-right: 0.25rem;
    padding-top: 0.80rem;
    padding-bottom: 0.75rem;
    background-color: #f6f6f6;
    padding-right: 0.75rem;
}

.THIS .filtermode .buttonsection {
    align-items: end;
    display: flex;
    justify-content: end;
}

.THIS .momstyle .dttablemain.cDT_Table {
    maargin-top: 1rem;
}

.THIS .momstyle .slds-grid.dttablemain.maincontent {
    margin-top: 1rem;
}

.THIS td {
    color: #a9afb6;
}

.THIS tr {
    height: 50px;
}

.THIS th {
    color: #525458;
}

.THIS .modal-header {
    text-align: left;
    background: #253d87 !important;
    color: white;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.THIS .footer button {
    --slds-c-button-color-background: #253d87;
    --slds-c-button-text-color: white;
    padding: 4px 26px;
    font-size: 16px;
}

.THIS .footer button:hover {
    background-color: rgb(38 61 135 / 84%);
    color: white;
}
.THIS .widthStyle{
    max-width: 2.55em;
}

.THIS .marginStyle{
margin-top: 2px;
}

.THIS .filterButtonStyle{
    max-width: 3rem;text-align: right;
}
.THIS .widthStyle2{
    max-width: 2em;
}
.THIS .nopadding{
    padding:unset !important;
  }