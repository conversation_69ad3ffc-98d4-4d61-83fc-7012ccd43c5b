<aura:component controller="DT_TableController" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
    access="global">

    <lightning:messageChannel type="cocmd__FilePreviewMessageChannel__c" aura:id="filechannel" scope="APPLICATION" />

    <!-- Opening NC edit modal on load of cmp if url has taskId -->
    <aura:method name="openNcModal" action="{!c.showNCEditModal}" description="open NC edit Modal">
        <aura:attribute name="taskId" type="String" />
    </aura:method>
    <!-- Opening CA edit modal on load of cmp if url has taskId -->
    <aura:method name="openCAModal" action="{!c.showCAEditModal}" description="open CA edit Modal">
        <aura:attribute name="taskId" type="String" />
    </aura:method>
    <!-- Opening Training Course EditModal on load of cmp if url has taskId -->
    <aura:method name="openTrainingCourseEditModal" action="{!c.showEmployeeTrainingEditModal}">
        <aura:attribute name="recId" type="String" />
    </aura:method>
    <!-- Opening Change Request edit modal on load of cmp if url has taskId -->
    <aura:method name="openDocChangeRequestModal" action="{!c.showCRDocumentModal}" description="open Change Request Modal">
        <aura:attribute name="taskId" type="String" />
    </aura:method>
    <aura:method name="openAuditPreviewModal" action="{!c.previewAudit}" description="Open Audit preview Modal">
        <aura:attribute name="taskId" type="String" />
    </aura:method>
    <aura:method name="openeditAuditChecklistModal" action="{!c.editAuditChecklistModal}" description="open Audit Checklist Edit Modal">
        <aura:attribute name="recId" type="String" />
    </aura:method>
    <!-- <aura:method name="openeditAuditChecklistModal" action="{!c.editAuditChecklistModal}" description="open Audit Checklist Edit Modal">
        <aura:attribute name="recId" type="String" />
    </aura:method> -->

    <!--Init handler which is call initRecords js function on component Load-->
    <aura:handler name="init" value="{!this}" action="{!c.initRecords}" />

    <aura:method name="initialize" action="{!c.refreshDataTable}" description="constructor"></aura:method>

    <aura:attribute type="String" name="listname" access="global" />
    <aura:attribute type="Boolean" name="editable" access="public" default="false" />
    <aura:attribute type="Boolean" name="editActionPlan" access="public" default="false" />

    <aura:attribute name="pageSize" type="Integer" default="10" access="public" />
    <aura:attribute name="offset" type="Integer" default="0" />
    <aura:attribute name="totalSize" type="Integer" />
    <aura:attribute name="start" type="Integer" />
    <aura:attribute name="end" type="Integer" />
    <aura:attribute name="totalPages" type="Integer" />
    <aura:attribute name="pageNumber" type="Integer" default="1" />
    <aura:attribute name="controlPrevious" type="string" />
    <aura:attribute name="controlNext" type="string" />

    <aura:attribute name="showSaveCancelBtn" type="boolean" />
    <aura:attribute name="showActionPlanLogEdit" access="global" type="boolean" />
    <aura:attribute name="showCheckbox" type="boolean" default="false" />
    <aura:attribute name="allowSearch" type="boolean" default="false" />
    <aura:attribute name="dataObj" type="Object" />
    <aura:attribute name="tableData" type="Object[]" />
    <aura:attribute name="tableColumns" type="Object[]" />
    <aura:attribute name="headerCheckbox" type="boolean" default="false" />
    <aura:attribute name="selectedRows" type="String[]" />
    <aura:attribute name="activeRow" type="String" default="" />
    <aura:attribute name="loaded" type="boolean" default="false" />
    <aura:attribute name="sortdirectionasc" type="boolean" default="false" />
    <aura:attribute name="sortcolumn" type="String" />
    <aura:attribute name="title" type="String" />
    <aura:attribute name="alloctionPassThrow" type="boolean" default="false" />

    <aura:attribute name="allDataList" type="Object[]" />
    <aura:attribute name="filteredDataList" type="Object[]" />

    <aura:attribute name="fromDate" type="Date" />
    <aura:attribute name="toDate" type="Date" />

    <aura:attribute name="gturl" type="String" default="/apex/GeneratePayInAdvice?id=" />

    <aura:attribute name="recordId" type="String" access="public" />

    <aura:attribute name="showActions" type="boolean" default="false" />
    <aura:attribute name="showAllocation" type="boolean" access="global" default="false" />
    <aura:attribute name="internalScreenRisk" type="boolean" default="false" />
    <aura:attribute name="internalScreenRiskDPD" type="boolean" default="false" />
    <aura:attribute name="allowFilter" type="boolean" default="false" />

    <aura:attribute name="apportion" type="String" />

    <!-- Audit -->
    <aura:attribute name="crApplicable" type="boolean" default="true" />
    <aura:attribute name="showEditAction" type="boolean" access="global" default="false" />
    <aura:attribute name="showCREditAction" type="boolean" access="global" default="false" />
    <aura:attribute name="showWOEditAction" access="global" type="boolean" default="false" />
    <aura:attribute name="showAssetEditAction" type="boolean" default="false" />
    <aura:attribute name="showThreeDotActionsInlined" access="global" type="boolean" default="false" />
    <aura:attribute name="showApprovalButtons" access="global" type="boolean" default="false" />
    <aura:attribute name="showAuthorButtons" access="global" type="boolean" default="false" />
    <aura:attribute name="showAddDocumentButton" access="global" type="boolean" default="false" />
    <aura:attribute name="showTrainingDashboardButtons" access="global" type="boolean" default="false" />
    <aura:attribute name="showGroupTrainingButton" access="global" type="boolean" default="false" />
    <aura:attribute name="showAssetButton" access="global" type="boolean" default="false" />
    <aura:attribute name="showWOButton" type="boolean" default="false" />
    <aura:attribute name="showAssetActions" access="global" type="boolean" default="false" />
    <aura:attribute name="showNewEditAgendaButton" type="boolean" default="false" />
    <aura:attribute name="showMaintenanceActions" access="global" type="boolean" default="false" />
    <aura:attribute name="showAddMeetingButton" access="global" type="boolean" default="false" />
    <aura:attribute name="showMeetingActions" access="global" type="boolean" default="false" />
    <aura:attribute name="showCompletedMeetingActionBtn" access="global" type="boolean" default="false" />
    <aura:attribute name="showMeetingMinutes" access="global" type="boolean" default="false" />
    <aura:attribute name="showCompletedMeetingMinutes" type="boolean" default="false" />
    <aura:attribute name="showMeetingMinutesPreview" type="boolean" default="false" />
    <aura:attribute name="showMOMEditAction" type="boolean" default="false" />
    <aura:attribute name="showActionPlanEditAction" type="boolean" default="false" />
    <aura:attribute name="showActionPlanAction" type="boolean" default="false" />
    <aura:attribute name="showPreview" type="boolean" default="false" />
    <aura:attribute name="showActionPlanTable" type="boolean" default="false" />
    <aura:attribute name="showNewActionPlanButton" type="boolean" default="false" />
    <aura:attribute name="showNLButton" access="global" type="boolean" default="false" />
    <aura:attribute name="showCALButton" access="global" type="boolean" default="false" />
    <aura:attribute name="showNCEditAction" access="global" type="boolean" default="false" />
    <aura:attribute name="showTrainingEditAction" access="global" type="boolean" default="false" />
    <aura:attribute name="showCompletedTrainingActionBtn" access="global" type="boolean" default="false" />
    <aura:attribute name="showCAEditAction" access="global" type="boolean" default="false" />
    <aura:attribute name="showAssetViewAction" type="boolean" default="false" />
    <aura:attribute name="showAssetPreview" type="boolean" default="true" />
    <aura:attribute name="showAdminPublishButtons" type="boolean" default="false" />
    <aura:attribute name="showSerialNumber" type="boolean" default="false" />

    <aura:attribute name="showCRModal" type="boolean" default="false" />
    <aura:attribute name="showInvite" type="boolean" default="false" />
    <aura:attribute name="showAuditButtons" access="global" type="boolean" default="false" />
    <aura:attribute name="showAuditChecklistModal" type="boolean" default="false" />
    <aura:attribute name="showMaintenanceModal" type="boolean" default="false" />
    <aura:attribute name="showNewEditAgendaModal" type="boolean" default="false" />
    <aura:attribute name="showWOEditModal" type="boolean" default="false" />
    <aura:attribute name="showMeetingEditModal" type="boolean" default="false" />
    <aura:attribute name="showMeetingMinuteButton" type="boolean" default="false" />
    <aura:attribute name="showAssetEditModal" type="boolean" default="false" />
    <aura:attribute name="showActionPlanEditModal" type="boolean" default="false" />
    <aura:attribute name="showAdditionalAssetModal" type="boolean" default="false" />
    <aura:attribute name="showDeleteModal" type="boolean" default="false" />
    <aura:attribute name="showConfirmationModal" type="boolean" default="false" />
    <aura:attribute name="recordToDelete" type="String" />
    <aura:attribute name="documentHeader" type="String" />

    <aura:attribute name="showTrainingActions" type="boolean" default="false" />
    <aura:attribute name="showTrainingFeedback" type="boolean" default="false" />
    <aura:attribute name="showTrainingFeedbackAdmin" type="boolean" default="false" />
    <aura:attribute name="showTrainingEffectiveness" type="boolean" default="false" />
    <aura:attribute name="showTrainingApprovalAction" type="boolean" default="false" />
    <aura:attribute name="showEmployeeTrainingEdit" type="boolean" default="false" />

    <aura:attribute name="showAddTrainingCourseBtn" access="global" type="boolean" default="false" />
    <aura:attribute name="showTrainingCourseActionBtn" access="global" type="boolean" default="false" />

    <aura:attribute name="showAuditChecklist" access="global" type="boolean" default="false" />
    <aura:attribute name="showAuditEdit" type="boolean" access="global" default="false" />
    <aura:attribute name="showAuditViewChecklist" type="boolean" default="false" />

    <aura:attribute name="filterClass" type="String" default="slds-col hidden" />
    <aura:attribute name="userOptions" type="List" />

    <aura:attribute name="momId" type="String" />
    <aura:attribute name="managementReviewId" type="String" />

    <aura:attribute name="deleteMessage" type="String" default="Do you want to delete the record?" />

    <aura:attribute name="showCRTable" access="global" type="boolean" default="false" />

    <aura:attribute name="cbody" type="Aura.Component" />
    <aura:attribute name="componentName" type="String" default="" />

    <aura:attribute name="sectionNamePreview" type="String" />
    <aura:attribute type="String" name="appendName" access="public" default="" />
    <!--For Opening CA Modal Call from DT_cell starts here-->
    <aura:attribute name="showCAPreviewMode" type="boolean" default="false" />
    <aura:method name="openCApreview" action="{!c.OpenCAPreviewModal}" access="public">
        <aura:attribute name="CA_id" type="String" default="" />
    </aura:method>

    <aura:if isTrue="{!v.showCAPreviewMode == true}">
        <div class="slds-col action width-12em">
            <c:cmtAddCorrectiveActionLog aura:id="casectionforPreview" onactioncall="{!c.actionHandler}" />
        </div>
    </aura:if>
    <!--For Opening CA Modal Call from DT_cell ends here-->
    <div>
        <div class="slds-grid dttablemain maincontent">
            <div class="slds-col">
                <lightning:card>
                    <div class="slds-grid slds-wrap toolbar">
                        <div class="slds-col title">{!v.title}</div>
                        <div class="slds-col"></div>
                        <div class="slds-col"></div>
                        <div class="slds-col"></div>

                        <aura:if isTrue="{!v.allowFilter == true}">
                            <div class="slds-col filteraction ">
                                <lightning:input name="filter" variant="label-hidden" type="date" placeholder="From Date" value="{!v.fromDate}" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.allowFilter == true}">
                            <div class="slds-col filteraction">
                                <lightning:input name="filter" variant="label-hidden" placeholder="To Date" type="date" value="{!v.toDate}" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.allowFilter == true}">
                            <div class="slds-col action widthStyle">
                                <lightning:buttonIcon class="icon-style" iconName="utility:filterList" alternativeText="Filter" title="Filter" onclick="{!c.refreshDataTable}" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.allowSearch == true}">
                            <div class="slds-col searchaction">
                                <div class="marginStyle">
                                    <lightning:input aura:id="searchbox" name="enter-search" type="search" placeholder="Search this list" variant="label-hidden" onchange="{! c.search }" />
                                </div>
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showNewEditAgendaButton == true}">
                            <div class="action toolbar-items">
                                <c:cmtNewEditAgendaButton showAgendaButton="true" onactioncall="{!c.actionHandler}" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showAssetButton == true}">
                            <div class="action toolbar-items">
                                <c:cmtAssetButtons showAssetButton="true" onactioncall="{!c.actionHandler}"></c:cmtAssetButtons>
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showWOButton == true}">
                            <div class="action toolbar-items">
                                <c:cmtWorkOrderButtons showWorkOrderButton="true" showWorkOrderModal="false" previewWOModal="false" onactioncall="{!c.actionHandler}" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showAuditButtons == true}">
                            <div class="action toolbar-items">
                                <c:cmtScheduleAuditButton selectedRecordIds="{!v.selectedRows}" onactioncall="{!c.actionHandler}" showButtonOnEdit="true" isDashboardview="{!v.listname}"/>
                            </div>
                            <!--<div class="action toolbar-items">
                            <c:cmtAddDepartmentButton selectedRecordIds="{!v.selectedRows}" onactioncall="{!c.actionHandler}"/>
                        </div>
                        <div class="action toolbar-items">
                            <c:cmtAddProcessButton selectedRecordIds="{!v.selectedRows}" onactioncall="{!c.actionHandler}"/>
                        </div>
                        <div class="action toolbar-items">
                            <c:cmtAddAuditeeButton selectedRecordIds="{!v.selectedRows}" onactioncall="{!c.actionHandler}"/> 
                        </div>
                        <div class="action toolbar-items">
                            <c:cmtAddAuditeeButton selectedRecordIds="{!v.selectedRows}" isAuditee = "true" onactioncall="{!c.actionHandler}"/>
                        </div>
                        <div class="action toolbar-items">
                            <c:cmtScheduleAudit selectedRecordIds="{!v.selectedRows}" onactioncall="{!c.actionHandler}"/> 
                        </div>-->
                        </aura:if>
                        <aura:if isTrue="{!v.showNLButton == true}">
                            <div class="action toolbar-items">
                                <c:cmtAddNonConformityLog onactioncall="{!c.actionHandler}" showNCButton="true" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showCALButton == true}">
                            <div class="action toolbar-items">
                                <c:cmtAddCorrectiveActionLog onactioncall="{!c.actionHandler}" showCAButton="true" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showAddDocumentButton == true}">
                            <div class="action toolbar-items">
                                <lightning:button label="Add New Document" onclick="{!c.showNewCRDocumentModal}" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showAddMeetingButton == true}">
                            <div class="action toolbar-items">
                                <c:cmtNewMeeting showNewMeetingButton="true" onactioncall="{!c.actionHandler}" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showAddTrainingCourseBtn == true}">
                            <div class="action toolbar-items">
                                <c:cmtCourseButtons aura:id="TrainingCourseModal" showCourseButton="true" onactioncall="{!c.actionHandler}" onopenTraining="{!c.openTraining}"/>
                                <c:trainingdashboardbuttons aura:id="trainingdashboard" showGroupTrainingButton="true" showEmpTrainingButton="true" showOrientationButton="false" showIndividualTraining="false" onactioncall="{!c.actionHandler}">
                                </c:trainingdashboardbuttons>
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showMeetingMinuteButton == true}">
                            <div class="action toolbar-items">
                                <lightning:button label="Mark Complete" onclick="{!c.showConfirmationModal}" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showTrainingDashboardButtons == true}">
                            <!-- show all buttons -->
                            <div class="action toolbar-items">
                                <c:trainingdashboardbuttons aura:id="trainingdashboard" showGroupTrainingButton="true" showEmpTrainingButton="true" showOrientationButton="false" showIndividualTraining="false" onactioncall="{!c.actionHandler}" onopenTraining="{!c.openTraining}">
                                </c:trainingdashboardbuttons>
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showGroupTrainingButton == true}">
                            <div class="action toolbar-items">
                                <c:trainingdashboardbuttons aura:id="trainingdashboard" showGroupTrainingButton="true" onopenTraining="{!c.openTraining}"></c:trainingdashboardbuttons>
                            </div>
                        </aura:if>
                        <aura:if isTrue="{!v.showSaveCancelBtn}">
                            <div class="slds-col action toolbar-items">
                                <!--button for save and cancel Record after Inline Edit-->
                                <lightning:button label="Cancel" onclick="{!c.cancel}" />
                            </div>
                            <div class="slds-col action toolbar-items">
                                <lightning:button label="Save" onclick="{!c.saveRecord}" variant="success" />
                            </div>
                        </aura:if>
                        <aura:if isTrue="true">
                            <div class="slds-col action widthStyle">
                                <lightning:buttonIcon class="icon-style" iconName="utility:refresh" alternativeText="Refresh" title="Refresh." onclick="{!c.refreshDataTable}" />
                            </div>
                        </aura:if>

                        <!--PAGE NAVIGATION-->
                        <div class="slds-col action pagination_button">
                            <lightning:buttonIcon class="icon-style" iconName="utility:left" variant="base" size="medium" alternativeText="Previous Page" onclick="{!c.previousPage}" disabled="{!v.start == 0}"></lightning:buttonIcon>
                        </div>
                        <div class="slds-col pagination_label">
                            <span>{!v.pageNumber} of {!v.totalPages}</span>
                        </div>
                        <div class="slds-col action pagination_button">
                            <lightning:buttonIcon class="icon-style" iconName="utility:right" variant="base" size="medium" alternativeText="Next Page" onclick="{!c.nextPage}" disabled="{!v.pageNumber == v.totalPages}">
                            </lightning:buttonIcon>
                        </div>
                        <aura:if isTrue="true">
                            <aura:if isTrue="{!v.listname == 'Completed_Training'}">
                            <div class="slds-col action toolbar-items filterButtonStyle">
                                 
                                <lightning:buttonIcon class="icon-style" iconName="utility:filterList" alternativeText="Filter" title="Filter" onclick="{!c.showFilterSection}" />
                            
                                </div>
                            </aura:if>
                        </aura:if>
                    </div>

                    <aura:if isTrue="{!!v.loaded }">
                        <lightning:spinner alternativeText="Loading" variant="brand" />
                    </aura:if>
                    <div class="table-wrap">
                        <table aria-multiselectable="true" class="slds-table slds-table_cell-buffer dt-table">
                            <thead>
                                <tr class="slds-line-height_reset">
                                    <aura:if isTrue="{!v.showSerialNumber}">
                                        <th class='widthStyle2' scope="col">
                                            <div class="slds-truncate">S. No.</div>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showPreview}">
                                        <th class='widthStyle2' scope="col">
                                            <div class="slds-truncate">&nbsp;</div>
                                            <span class="hidden">showPreview</span>
                                        </th>
                                    </aura:if>

                                    <aura:if isTrue="{!v.showCheckbox == true}">
                                        <th class='widthStyle2 slds-align_absolute-center' scope="col">
                                            <lightning:input name="commoncheckbox" type="checkbox" checked="{!v.headerCheckbox}" onchange="{!c.selectAll}" />
                                        </th>
                                    </aura:if>
                                    <aura:iteration items="{!v.tableColumns}" var="item">
                                            <th class="{! v.sortcolumn != null &amp;&amp; v.sortcolumn == item.sortColumn ? 'slds-is-sortable slds-text-align_left slds-is-sorted nopadding' : 'slds-is-sortable slds-text-align_left nopadding' }" scope="col">
                                                <lightning:button variant="base" aura:id="{!item.key}" value="{!item.sortColumn}" class="slds-th__action slds-text-link_reset slds-text-align_left" onclick="{!c.sort}">
                                                    <span aura:id="{!item.key}" class="slds-assistive-text">{!'Sort by: '+item.columnName}</span>
                                                    <div aura:id="{!item.key}" class="slds-grid slds-grid_vertical-align-center slds-has-flexi-truncate slds-float_left">
                                                        <span class="slds-truncate ">{!item.columnName}</span>
                                                        <aura:if isTrue="{!v.sortdirectionasc == true}">
                                                            <span class="slds-icon_container slds-icon-utility-arrowdown">
                                                                <lightning:icon iconName="utility:arrowdown" alternativeText="{!'Sort by: '+item.columnName}" title="{!'Sort by: '+item.columnName}" size="xx-small"
                                                                    class="slds-icon slds-icon-text-default slds-is-sortable__icon" />
                                                            </span>
                                                        </aura:if>
                                                        <aura:if isTrue="{!v.sortdirectionasc != true}">
                                                            <span class="slds-icon_container slds-icon-utility-arrowup">
                                                                <lightning:icon iconName="utility:arrowup" alternativeText="{!'Sort by: '+item.columnName}" title="{!'Sort by: '+item.columnName}" size="xx-small"
                                                                    class="slds-icon slds-icon-text-default slds-is-sortable__icon" />
                                                            </span>
                                                        </aura:if>
                                                    </div>
                                                </lightning:button>
                                            </th>
                                    </aura:iteration>
                                    <aura:if isTrue="{!v.showEditAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showEditAction</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showAuditEdit}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showAuditEdit</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showWOEditAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showWOEditAction</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showNCEditAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showNCEditAction</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showTrainingEditAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showTrainingEditAction</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showCAEditAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showCAEditAction</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showActionPlanEditAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showActionPlanEditAction</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showMOMEditAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showMOMEditAction</span>
                                        </th>
                                    </aura:if>

                                    <aura:if isTrue="{!v.showMaintenanceActions}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showMaintenanceActions</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showAssetActions}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showAssetActions</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showCREditAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showCREditAction</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showMeetingActions}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showMeetingActions</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showCompletedMeetingActionBtn}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showCompletedMeetingActionBtn</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showCompletedTrainingActionBtn}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showCompletedTrainingActionBtn</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="false">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showThreeDotActionsInlined</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showApprovalButtons}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showApprovalButtons</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showAuthorButtons}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showAuthorButtons</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showAdminPublishButtons}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showAdminPublishButtons</span>
                                        </th>
                                    </aura:if>
                                    <!-- <aura:if isTrue="{!v.showEmployeeTrainingEdit}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showEmployeeTrainingEdit</span>
                                        </th>
                                    </aura:if> -->
                                    <aura:if isTrue="{!v.showTrainingActions}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showTrainingActions</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showTrainingApprovalAction}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showTrainingApprovalAction</span>
                                        </th>
                                    </aura:if>
                                    <aura:if isTrue="{!v.showTrainingCourseActionBtn}">
                                        <th class="" scope="col">
                                            &nbsp;
                                            <span class="hidden">showTrainingCourseActionBtn</span>
                                        </th>
                                    </aura:if>

                                </tr>
                            </thead>
                            <tbody>
                                <aura:if isTrue="{!and(v.tableData != null,v.tableData.length > 0 )}">
                                    <aura:iteration items="{!v.tableData}" var="item" indexVar="index">
                                        <tr aria-selected="false" class="{!if(v.activeRow == item.key,'active-row','inactive-row')}" id="{!item.key}" aura:id="{!item.key}" onclick="{!c.setActiveRow}" data-cmp="{!item.componentName}">
                                            <aura:if isTrue="{!v.showSerialNumber}">
                                                <c:DT_Cell rowId="{!item.key}" isEditable="false" cellValue="{!v.offset+index+1}" altValue="" dataType="STRING" isClickable="false" showSaveCancelBtn="false" />
                                            </aura:if>
                                            <aura:if isTrue="{!v.showPreview}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:desktop" variant="bare" onclick="{! c.showWOEditModal }" alternativeText="Preview" title="Preview" value="{!item.key}" />
                                                </td>
                                            </aura:if>

                                            <aura:if isTrue="{!v.showCheckbox == true}">
                                                <c:DT_Cell onCellChange="{!c.onSelect}" rowId="{!item.key}" isEditable="true" cellValue="{!item.isSelected}" altValue="" dataType="CHECKBOX" isClickable="false" showSaveCancelBtn="false" />
                                            </aura:if>
                                            <aura:iteration items="{!item.dataTableRecordList}" var="subitem">
                                                <aura:if isTrue="{!and(v.dataObj.objectName == 'Management_Review__c',subitem.columnAPIName=='Name')}">
                                                    <td>
                                                        {!subitem.columnValue}
                                                    </td>
                                                </aura:if>
                                                <aura:if isTrue="{!and(v.dataObj.objectName == 'Document__c',subitem.columnAPIName=='Name')}">
                                                    <td>
                                                        {!subitem.columnValue}
                                                    </td>
                                                </aura:if>
                                                <aura:if isTrue="{!not(and(or(v.dataObj.objectName == 'Management_Review__c', v.dataObj.objectName == 'Document__c'),subitem.columnAPIName=='Name'))}">
                                                    <c:DT_Cell parentAction="{!this}" onCellChange="{!c.onSelect}" rowId="{!item.key}" isComponent="{!subitem.isComponent}" componentName="{!subitem.componentName}" row="{!item}"
                                                        isEditable="{!and(v.editable,subitem.isEditable)}" cellValue="{!subitem.columnValue}" columnAPIName="{!subitem.columnAPIName}" columnName="{!subitem.columnName}"
                                                        altValue="{!subitem.columnAltValue}" dataType="{!subitem.columnType}" columnDataType="{!subitem.columnDataType}" isClickable="{!subitem.isClickable}"
                                                        showSaveCancelBtn="{!v.showSaveCancelBtn}" />
                                                </aura:if>
                                            </aura:iteration>
                                            <aura:if isTrue="{!v.showEditAction}">
                                                <td>
                                                    <aura:if isTrue="{!item.status != 'Obsolete'}">
                                                        <lightning:buttonIcon class="icon-style" iconName="utility:rotate" variant="bare" onclick="{! c.showCRModal }" alternativeText="Refresh" title="Change Request" value="{!item.key}" />
                                                    </aura:if>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showMasterDocumentModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:download" variant="bare" onclick="{! c.downloadFileCall }" alternativeText="Download" title="Download" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showDocDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showAuditEdit}">
                                                <td>
                                                    <aura:if isTrue="{!v.showAuditViewChecklist == true}">
                                                        <lightning:buttonIcon class="icon-style" iconName="utility:preview" variant="bare" onclick="{! c.previewAuditChecklistModal }" alternativeText="View" title="View"
                                                            value="{!item.key}" />
                                                    </aura:if>
                                                    <aura:if isTrue="{!v.showAuditViewChecklist != true}">
                                                        <lightning:buttonIcon class="icon-style" iconName="utility:preview" variant="bare" onclick="{! c.previewAudit}" alternativeText="View" title="View" value="{!item.key}" />
                                                    </aura:if>
                                                    <aura:if isTrue="{!v.showAuditViewChecklist == true}">
                                                        <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.editAuditChecklistModal}" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    </aura:if>
                                                    <aura:if isTrue="{!v.showAuditViewChecklist != true}">
                                                        <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showAuditSetupModal}" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    </aura:if>

                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showAuditDeleteModal}" alternativeText="Delete" title="Delete" value="{!item.key}" />

                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showActionPlanEditAction}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showActionPlanEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showActionPlanDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showActionPlanLogEdit}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showActionPlanLogEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showCREditAction}">
                                                <td>
                                                    <!-- <aura:if isTrue="{!v.crApplicable}">
                                                    <lightning:buttonIcon iconName="utility:rotate" variant="bare" onclick="{! c.showCRModal }" alternativeText="Refresh" title="Change Request" value="{!item.key}"/>
                                                </aura:if> -->
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showCRDocumentModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:download" variant="bare" onclick="{! c.downloadFileCall }" alternativeText="Download" title="Download" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showDocDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showWOEditAction}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:preview" variant="bare" onclick="{! c.previewWOModal }" alternativeText="View" title="View" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showWOEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showWODeleteModal }" alternativeText="Delete" title="Delete." value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showMOMEditAction}">
                                                <td>
                                                    <aura:if isTrue="{!v.showMeetingMinutesPreview == true}">
                                                        <lightning:buttonIcon class="icon-style slds-float_right slds-m-right_large" iconName="utility:edit" variant="bare" onclick="{! c.showAgendaEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    </aura:if>
                                                    <aura:if isTrue="{!v.showMeetingMinutesPreview != true}">
                                                        <lightning:buttonIcon class="icon-style slds-float_right slds-m-right_large" iconName="utility:preview" variant="bare" onclick="{! c.previewAgendaModal }" alternativeText="View" title="View" value="{!item.key}" />
                                                    </aura:if>

                                                    <aura:if isTrue="{!v.showActionPlanAction}">
                                                        <lightning:buttonIcon class="icon-style slds-float_right slds-m-right_large" iconName="utility:edit_form" variant="bare" onclick="{! c.showActionPlanNewModal }" alternativeText="Action Plan" title="Action Plan"
                                                            value="{!item.key}" />
                                                    </aura:if>
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showNCEditAction}">
                                                <td>
                                                    <lightning:buttonIcon aura:id="show" class="icon-style" iconName="utility:preview" variant="bare" onclick="{! c.previewNCModal }" alternativeText="View" title="View" value="{!item.key}" />
                                                    <lightning:buttonIcon aura:id="edit" class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showNCEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showNCDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showTrainingEditAction}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:preview" variant="bare" onclick="{! c.showTrainingPreviewModal }" alternativeText="View" title="View" value="{!item.key}"/>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showEmployeeTrainingEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showTrainingDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showCAEditAction}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" aura:id="show" iconName="utility:preview" variant="bare" onclick="{! c.showCAPreviewModal }" alternativeText="View" title="View"
                                                        value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showCAEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showCADeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showMaintenanceActions}">
                                                <td>
                                                    <!-- <aura:if isTrue="{!v.showAssetViewAction}">
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:preview" variant="bare" onclick="{! c.showAssetComponent }" alternativeText="View Asset Additional Details and Paramteers" title="View Asset Additional Details and Parameters" value="{!item.key}"/>
                                                </aura:if> -->
                                                    <!-- <lightning:buttonIcon class="icon-style" iconName="utility:shift_pattern" variant="bare" onclick="{! c.showMaintenanceModal }" alternativeText="Schedule Maintenance" title="Schedule Maintenance" value="{!item.key}"/> -->
                                                    <lightning:buttonIcon class="icon-style" aura:id="show" iconName="utility:preview" variant="bare" onclick="{! c.previewAssetModal }" alternativeText="View" title="View"
                                                        value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showAssetEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showWODeleteModal }" alternativeText="Delete" title="Delete." value="{!item.key}" />
                                                    <!-- <lightning:buttonIcon class="icon-style" iconName="utility:assignment" variant="bare" onclick="{! c.additionalParameters }" alternativeText="Add Asset Paramteers" title="Add Asset Paramteers" value="{!item.key}"/> -->
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showAssetActions}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:shift_pattern" variant="bare" onclick="{! c.showCRModal }" alternativeText="Refresh" title="Change Request"
                                                        value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:download" variant="bare" onclick="{! c.downloadFileCall }" alternativeText="Download" title="Download" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="false">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:archive" variant="bare" onclick="{! c.showDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showApprovalButtons}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:check" variant="bare" onclick="{! c.setCRStatusCallApprove }" alternativeText="Approve" title="Approve" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:close" variant="bare" onclick="{! c.setCRStatusCallReject }" alternativeText="Reject" title="Reject" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showMeetingActions}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showMeetingEditModal }" alternativeText="Edit Meeting" title="Edit Meeting"
                                                        value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:email" variant="bare" onclick="{! c.showInviteModal }" alternativeText="Send Reminder Email" title="Send Reminder Email"
                                                        value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showMeetingDeleteModal }" alternativeText="Delete" title="Delete" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showCompletedMeetingActionBtn}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" aura:id="show" iconName="utility:preview" variant="bare" onclick="{! c.previewMeetingModal }" alternativeText="View" title="View"
                                                        value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showCompletedTrainingActionBtn}">
                                                <td>
                                                    <lightning:buttonIcon aura:id="show" class="icon-style" iconName="utility:preview" variant="bare" onclick="{! c.showTrainingPreviewModal }" alternativeText="View" title="View"
                                                        value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showAuthorButtons}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:check" variant="bare" onclick="{! c.setCRAuthorStatusCallApprove }" alternativeText="Approve" title="Approve"
                                                        value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:close" variant="bare" onclick="{! c.setCRAuthorStatusCallReject }" alternativeText="Reject" title="Reject" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showAdminPublishButtons}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:push" variant="bare" onclick="{! c.setPublishDoc }" alternativeText="Publish" title="Publish" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:undo" variant="bare" onclick="{! c.setDraftDoc }" alternativeText="Draft" title="Draft" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showTrainingApprovalAction}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:check" variant="bare" onclick="{! c.setTrainingApprove }" alternativeText="Approve" title="Approve" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:close" variant="bare" onclick="{! c.setTrainingReject }" alternativeText="Reject" title="Reject" value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showTrainingActions}">
                                                <td>
                                                    <aura:if isTrue="{!v.showTrainingFeedbackAdmin}">
                                                        <c:cmtTrainingFeedbackAdmin recordId="{!item.key}" />
                                                    </aura:if>
                                                    <aura:if isTrue="{!v.showTrainingFeedback}">
                                                        <c:cmtTrainingFeedback recordId="{!item.key}" />
                                                    </aura:if>
                                                    <aura:if isTrue="{!v.showTrainingEffectiveness}">
                                                        <c:cmtTrainingEffectiveness recordId="{!item.key}" />
                                                    </aura:if>
                                                </td>
                                            </aura:if>
                                            <aura:if isTrue="{!v.showTrainingCourseActionBtn}">
                                                <td>
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:preview" variant="bare" onclick="{! c.showTrainingCoursePreviewModal }" alternativeText="Preview" title="Preview"
                                                        value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:edit" variant="bare" onclick="{! c.showTrainingCourseEditModal }" alternativeText="Edit" title="Edit" value="{!item.key}" />
                                                    <lightning:buttonIcon class="icon-style" iconName="utility:delete" variant="bare" onclick="{! c.showTrainingCourseDeleteModal }" alternativeText="Delete" title="Delete"
                                                        value="{!item.key}" />
                                                </td>
                                            </aura:if>
                                            <!-- <aura:if isTrue="{!v.showAuditEdit}">
                                            <td>
                                                <c:cmtAuditChecklist recordId="{!item.key}"/>
                                            </td>
                                        </aura:if> -->
                                        </tr>
                                    </aura:iteration>
                                </aura:if>
                            </tbody>
                        </table>
                    </div>
                    <aura:if isTrue="{!or(v.tableData == null,v.tableData.length == 0 )}">
                        <div class="slds-card__body slds-card__body_inner">
                            <div class="slds-illustration slds-illustration_small">
                                <div class="slds-text-longform">
                                    <p class="slds-text-body_regular">
                                        <c:cmtNoDataImage />
                                    </p>
                                </div>
                            </div>
                        </div>
                    </aura:if>

                    <!-- use aura:if for show/hide buttons -->
                    <aura:if isTrue="{!v.showCRModal}">
                        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
                            <div class="slds-modal__container">
                                <header class="slds-modal__header modal-header">
                                    <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">{!v.documentHeader}</h2>
                                </header>
                                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                                    <c:cmtNewOrChangeRequestForm sectionNamePreview="{!v.sectionNamePreview}" aura:id="crcmp" onactioncall="{!c.actionHandler}" />
                                </div>
                            </div>
                        </section>
                        <div class="slds-backdrop slds-backdrop_open"></div>
                    </aura:if>
                    <aura:if isTrue="{!v.showDeleteModal}">
                        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
                            <div class="slds-modal__container">
                                <header class="slds-modal__header modal-header">
                                    <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">Delete Record</h2>
                                </header>
                                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                                    {!v.deleteMessage}
                                </div>
                                <footer class="slds-modal__footer footer">
                                    <button class="slds-button slds-button_neutral" onclick="{!c.hideDeleteModal}">Cancel</button>
                                    <button class="slds-button slds-button_brand" onclick="{!c.deleteRecordCall}">Delete</button>
                                </footer>
                            </div>
                        </section>
                        <div class="slds-backdrop slds-backdrop_open"></div>
                    </aura:if>
                    <aura:if isTrue="{!v.showConfirmationModal}">
                        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1">
                            <div class="slds-modal__container">
                                <header class="slds-modal__header modal-header">
                                    <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">Confirmation</h2>
                                </header>
                                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                                    Are you sure you want to complete this meeting.
                                </div>
                                <footer class="slds-modal__footer footer">
                                    <button class="slds-button slds-button_neutral" onclick="{!c.makeMeetingComplete}">Yes, I want to complete this meeting</button>
                                    <button class="slds-button slds-button_brand" onclick="{!c.hideConfirmationModal}">Cancel</button>
                                </footer>
                            </div>
                        </section>
                        <div class="slds-backdrop slds-backdrop_open"></div>
                    </aura:if>
                    <aura:if isTrue="{!v.showMaintenanceModal}">
                        <c:cmtAssetScheduleMaintenance aura:id="assetSchedule" onactioncall="{!c.actionHandler}"></c:cmtAssetScheduleMaintenance>
                    </aura:if>
                    <aura:if isTrue="{!v.showAdditionalAssetModal}">
                        <c:cmtAssetAdditionalDetail aura:id="additonalAssetParam" onactioncall="{!c.actionHandler}"></c:cmtAssetAdditionalDetail>
                    </aura:if>
                    <aura:if isTrue="{!v.showWOEditModal}">
                        <c:cmtWorkOrderButtons aura:id="workordersection" onactioncall="{!c.actionHandler}"></c:cmtWorkOrderButtons>
                    </aura:if>
                    <aura:if isTrue="{!v.showAssetEditModal}">
                        <c:cmtAssetButtons aura:id="assetsection" onactioncall="{!c.actionHandler}"></c:cmtAssetButtons>
                    </aura:if>
                    <aura:if isTrue="{!v.showAuditChecklistModal}">
                        <c:cmtAuditChecklist aura:id="auditChecklist" onactioncall="{!c.actionHandler}"></c:cmtAuditChecklist>
                    </aura:if>
                    <aura:if isTrue="true">
                        <c:cmtActionPlanButton aura:id="actionplansection" onactioncall="{!c.refreshActionPlan}"></c:cmtActionPlanButton>
                    </aura:if>
                    <aura:if isTrue="true">
                        <c:cmtNewMeeting aura:id="meetingsection" showNewMeetingModal="false" onactioncall="{!c.actionHandler}"></c:cmtNewMeeting>
                    </aura:if>
                    <aura:if isTrue="{!v.showNewEditAgendaModal}">
                        <c:cmtNewEditAgendaButton aura:id="agendasection" onactioncall="{!c.actionHandler}"></c:cmtNewEditAgendaButton>
                    </aura:if>
                </lightning:card>
            </div>
            <div class="{!v.filterClass}">
                <div class="slds-grid slds-grid_vertical slds-gutters_direct filtermode">
                    <div class="slds-col">
                        <aura:iteration items="{!v.filteredDataList}" var="filter" indexVar="indexFilter">
                            <aura:if isTrue="{!filter.dataType == 'String'}">
                                <lightning:input name="{!filter.label}" label="{!filter.label}" value="{!filter.value}" />
                            </aura:if>
                            <aura:if isTrue="{!filter.dataType == 'Number'}">
                                <lightning:input type="number" name="{!filter.label}" label="{!filter.label}" value="{!filter.value}" />
                            </aura:if>
                            <aura:if isTrue="{!filter.dataType == 'Date'}">
                                <lightning:input type="date" name="{!filter.label}" label="{!filter.label}" value="{!filter.value}" />
                            </aura:if>
                            <aura:if isTrue="{!filter.dataType == 'DateTime'}">
                                <lightning:input type="datetime" name="{!filter.label}" label="{!filter.label}" value="{!filter.value}" />
                            </aura:if>
                            <aura:if isTrue="{!filter.dataType == 'Picklist'}">
                                <!-- <lightning:input type="datetime" name="{filter.label}" label="{!filter.label}" value="{!filter.value}"/>                             -->
                                <lightning:select value="{!filter.value}" name="{!filter.label}" label="{!filter.label}">
                                    <option value="">--None--</option>
                                    <aura:iteration items="{!filter.picklistValues}" var="val" indexVar="key">
                                        <option value="{!val}" text="{!val}" />
                                    </aura:iteration>
                                </lightning:select>
                            </aura:if>
                            <aura:if isTrue="{!filter.dataType == 'Reference'}">
                                <c:customLookupLwc sObjectApiName="{!filter.objectName}" label="{!filter.label}" onlookupupdate="{!c.lookupRecord}" indexParent="{!indexFilter}" defaultRecordId="{!filter.value}" placeholder="type here...">
                                </c:customLookupLwc>
                            </aura:if>
                        </aura:iteration>
                    </div>
                    <div class="slds-col action buttonsection">
                        <div class="slds-form-element__control">
                            <lightning:button variant="brand" label="Apply Filter" title="Brand action" onclick="{! c.applyFilter }" />
                        </div>
                    </div>
                    <!-- <div class="slds-col">
                    <div class="slds-form-element">
                        <label class="slds-form-element__label" for="form-element-01">Document Status</label>
                        <div class="slds-form-element__control">
                          <input type="text" id="form-element-01" placeholder="Placeholder text…" class="slds-input" />
                        </div>
                    </div>
                </div> -->
                </div>
            </div>
        </div>
    </div>
    <div class="dttablemain">
        <aura:if isTrue="{!v.showAssetPreview}">
            <c:cmtViewAssetDetail aura:id="assetview"></c:cmtViewAssetDetail>
        </aura:if>
        <aura:if isTrue="{!v.showPreview}">
            <lightning:card>
                <c:cmtRecordPreview></c:cmtRecordPreview>
            </lightning:card>
        </aura:if>
        <aura:if isTrue="{!and(v.showMeetingMinutes,v.recordId != null)}">
            <div class="momstyle">
                <c:DT_Table aura:id="momtable" title="Minutes of Meeting" listname="Minutes_of_Meeting_Admin" recordId="{!v.recordId}" showActionPlanAction="false" showMOMEditAction="true" showNewEditAgendaModal="true"
                    showNewEditAgendaButton="false" showMeetingMinuteButton="true"></c:DT_Table>

            </div>
        </aura:if>
        <aura:if isTrue="{!and(v.showCompletedMeetingMinutes,v.recordId != null)}">
            <div class="momstyle">
                <c:DT_Table aura:id="momtable" title="Minutes of Meeting" showMeetingMinutesPreview="true" listname="Minutes_of_Meeting_Admin" recordId="{!v.recordId}" showActionPlanAction="false" showMOMEditAction="true"
                    showNewEditAgendaModal="true" showNewEditAgendaButton="false" showMeetingMinuteButton="true"></c:DT_Table>
            </div>
        </aura:if>
        <aura:if isTrue="{!v.showActionPlanTable}">
            <div class="momstyle">
                <c:DT_Table aura:id="actionplantable" showSerialNumber="false" title="Action Plan" listname="ActionPlan_Admin" recordId="{!v.recordId}" showActionPlanEditAction="true"></c:DT_Table>

            </div>
        </aura:if>
        <aura:if isTrue="{!v.showCRTable}">
            <div class="momstyle">
                <c:DT_Table aura:id="crtable" showSerialNumber="false" listname="Document_Revision_History" recordId="{!v.recordId}"></c:DT_Table>
            </div>
        </aura:if>
        <aura:if isTrue="{!v.showNCEditAction == true}">
            <div class="slds-col action width-12em">
                <c:cmtAddNonConformityLog aura:id="ncsection" onactioncall="{!c.actionHandler}" />
            </div>
        </aura:if>
            <div class="slds-col action width-12em">
                <c:cmtAddCorrectiveActionLog aura:id="casection" onactioncall="{!c.actionHandler}" />
            </div>
        <aura:if isTrue="true">
            <div class="slds-col action width-12em">
                <c:cmtSendCalendarInvite aura:id="invite" />
            </div>
        </aura:if>
        <aura:if isTrue="{!v.showTrainingEditAction}">
            <div class="slds-col action width-12em">
                <c:cmtTrainingEdit aura:id="trainingedit" />
            </div>
        </aura:if>
        <aura:if isTrue="{!v.showCompletedTrainingActionBtn}">
            <div class="slds-col action width-12em">
                <c:cmtTrainingEdit aura:id="trainingedit" onactioncall="{!c.actionHandler}" />
            </div>
        </aura:if>
        <aura:if isTrue="{!v.showEmployeeTrainingEdit}">
            <div class="slds-col action width-12em">
                <c:cmtEmployeeTrainingForm aura:id="employeetraining" onactioncall="{!c.actionHandler}" />
            </div>
        </aura:if>
        <aura:if isTrue="{!v.showAuditEdit == true}">
            <div class="action toolbar-items">
                <c:cmtScheduleAuditButton aura:id="auditschedulebtn" selectedRecordIds="{!v.selectedRows}" onactioncall="{!c.actionHandler}"   />
            </div>
        
     
        </aura:if>
        <div aura:id="placeholder">
            {!v.cbody}
        </div>
    </div>
</aura:component>