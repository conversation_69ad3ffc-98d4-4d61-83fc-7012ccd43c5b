<aura:component controller="CMT_AssetDashboardCtrl" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global">
    <!-- <aura:handler name="init" value="{!this}" action="{!c.initRecords}"/>     -->
    <aura:attribute name="ViewName" type="String" default="Admin"/>
    <aura:attribute name="TabName" type="String" access="global" default="Dashboard"/>

    <div>
        <div class="dashboard">
            <div class="notification">
    <aura:if isTrue="{!v.TabName == 'Dashboard'}">
        <c:cmtDashboardStatus modulename="asset" viewname="{!v.ViewName}" onactioncall="{!c.actionHandler}" viewId="3"/>
    </aura:if>
    </div>
    <aura:if isTrue="{!v.TabName == 'Dashboard'}">
        <aura:if isTrue="{!v.ViewName == 'Admin'}">
            <c:DT_Table listname="Asset_Dashboard" showWOEditAction="true" showWOButton="true" />
        </aura:if> <!--Admin-->
        <aura:if isTrue="{!v.ViewName == 'AssetOpen'}">
            <c:DT_Table listname="Asset_Dashboard_OPen" showWOEditAction="true" showWOButton="true" />
        </aura:if> <!--Employee-->
        <aura:if isTrue="{!v.ViewName == 'AssetOverDue'}">
            <c:DT_Table listname="Asset_Dashboard_Over_Due"/>
        </aura:if> <!--Owner View -->
    </aura:if>
    <aura:if isTrue="{!v.TabName == 'Asset Register'}">
        <aura:if isTrue="{!v.ViewName == 'Admin'}">
            <c:DT_Table listname="Asset_Master" showMaintenanceActions="true" showAssetButton="true" showAssetEditAction="true" showAssetViewAction="true"/> 
        </aura:if> <!--Approver View -->
        <aura:if isTrue="{!or(v.ViewName == 'Owner',v.ViewName == 'Approver')}">
            <c:DT_Table listname="Asset_Master" showMaintenanceActions="true" showAssetViewAction="true"/>
        </aura:if> <!--Owner View -->
    </aura:if>
    </div>
    </div>
</aura:component>