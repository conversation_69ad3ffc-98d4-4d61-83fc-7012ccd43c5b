<aura:component controller="CMT_DocumentDashboardLWCCtrl" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global">
    <!-- <aura:handler name="init" value="{!this}" action="{!c.initRecords}"/>     -->
    <aura:attribute name="ViewName" type="String" default="Admin"/>
    <aura:attribute access="global" name="TabName" type="String" default="Dashboard"/>
    <div>
        <div class="dashboard">
            <div class="notification">
    <aura:if isTrue="{!v.TabName == 'Master List'}">
        <c:cmtDashboardStatus modulename="document" viewname="{!v.ViewName}" onactioncall="{!c.actionHandler}" viewId="1"/>
    </aura:if>
    </div>
    <aura:if isTrue="{!v.TabName == 'Dashboard'}">
        <aura:if isTrue="{!v.ViewName == 'Admin'}">
            <c:DT_Table listname="Document_Dashboard_Is_Admin" showCREditAction="true" showThreeDotActionsInlined="true"/>
        </aura:if> <!--Admin-->
        <aura:if isTrue="{!v.ViewName == 'Owner'}">
            <c:DT_Table listname="Document_Dashboard_current_user_is_owner"/>
        </aura:if> <!--Owner View -->
        <aura:if isTrue="{!v.ViewName == 'Approver'}">
            <c:DT_Table listname="Document_Dashboard_current_user_is_appro" showApprovalButtons="true" showCREditAction="true"/> 
        </aura:if> <!--Approver View -->
        <aura:if isTrue="{!v.ViewName == 'Author'}">
            <c:DT_Table listname="Document_Dashboard_current_user_is_autho" showAuthorButtons="true" showCREditAction="true" crApplicable="false"/> 
        </aura:if> <!--Author View -->
        <aura:if isTrue="{!v.ViewName == 'OwnerPA'}">
            <c:DT_Table listname="Document_Dashboard_status_is_pending" showCREditAction="true"/>
        </aura:if> <!--Owner View but Pending Approval -->
    </aura:if>
    <aura:if isTrue="{!v.TabName == 'Master List'}">
        <aura:if isTrue="{!v.ViewName == 'Admin'}">
            <c:DT_Table listname="Document_Master_List" showCRTable="true" showEditAction="true" showAddDocumentButton="true" showThreeDotActionsInlined="true"/> 
        </aura:if> <!--Approver View -->
        <aura:if isTrue="{!or(v.ViewName == 'Owner',v.ViewName == 'Approver')}">
            <c:DT_Table listname="Document_Master_List" showAddDocumentButton="true" showThreeDotActionsInlined="true"/>
        </aura:if> <!--Owner View -->
        <aura:if isTrue="{!v.ViewName == 'Author'}">
            <c:DT_Table listname="Document_Master_List" showEditAction="true" showThreeDotActionsInlined="true"/>
        </aura:if> <!--Owner View -->
    </aura:if>
    </div>
    </div>
</aura:component>