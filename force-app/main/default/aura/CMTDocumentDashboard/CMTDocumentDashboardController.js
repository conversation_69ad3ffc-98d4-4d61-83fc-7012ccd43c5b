({
    actionHandler : function(component, event, helper) {
        component.set('v.ViewName',event.getParam('targetName'));
    },

    initRecords : function(component, event, helper) {
        var action = component.get("c.currentUserInfo");
        action.setCallback(this, function(response) {
            var result = response.getReturnValue();
            if(result.profileName == 'Community Admin') {
                component.set('v.ViewName','Admin')
            } else if(result.profileName == 'Community Approver') {
                component.set('v.ViewName','Approver')
            } else if(result.profileName == 'Author') {
                component.set('v.ViewName','Author')
            } else {
                component.set('v.ViewName','Owner')
            }
            
        });
        $A.enqueueAction(action);
    }
})