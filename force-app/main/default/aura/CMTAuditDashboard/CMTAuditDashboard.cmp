<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global">
    <aura:attribute name="ViewName" type="String" default="Admin"/>
    <aura:attribute name="recordId" type="String" default="Admin"/>
    <aura:attribute name="TabName" type="String" default="Dashboard"/>
    <div>
        <div class="dashboard">
            <div class="notification">
    <aura:if isTrue="{!v.TabName == 'Dashboard'}">
        <c:cmtDashboardStatus modulename="audit" viewname="{!v.ViewName}" onactioncall="{!c.actionHandler}" viewId="13"/>
    </aura:if>
    </div>
    <aura:if isTrue="{!v.TabName == 'Dashboard'}"> 
        <aura:if isTrue="{!v.ViewName == 'Admin'}">

            <c:DT_Table showSerialNumber="false" listname="Dashboard_Upcoming_Audits" showAuditEdit="true" showAuditViewChecklist="true" showAuditButtons="true"/>
        </aura:if> <!--Admin-->
        <aura:if isTrue="{!v.ViewName == 'openAudit'}">
            <c:DT_Table showSerialNumber="false" listname="Dashboard_Open_Audits" showAuditEdit="false" showAuditViewChecklist="false" showAuditButtons="true"/>
        </aura:if> <!--Owner View -->
        <aura:if isTrue="{!v.ViewName == 'overdueAudit'}">
            <c:DT_Table showSerialNumber="false" listname="Dashboard_OverDue_Audits" showAuditEdit="false" showAuditViewChecklist="false" showAuditButtons="true"/>
        </aura:if> <!--Owner View -->
        <aura:if isTrue="{!v.ViewName == 'auditScheduled'}">
            <c:DT_Table showSerialNumber="false" listname="Dashboard_Open_Audits" showAuditEdit="false" showAuditViewChecklist="false" showAuditButtons="true"/>

        </aura:if> <!--Owner View -->
    </aura:if>
    </div>
    </div>
</aura:component>