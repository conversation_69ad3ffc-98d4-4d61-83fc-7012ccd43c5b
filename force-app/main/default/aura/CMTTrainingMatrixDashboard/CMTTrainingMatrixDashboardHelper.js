({
	refreshTable: function(component, event, helper, fieldName) {
        component.set('v.loaded', false);
        // call the apex class method and fetch Contact list  
        var action = component.get("c.loadColumnsAndDataOffset");
        action.setParams({
            'metdataName': 'Completed_Training',
            'columnAPIName' : fieldName,
            'sortdirection' : component.get('v.sortdirectionasc'),
            'recordId' : component.get('v.recordId'),
            'fromDate' : component.get('v.fromDate'),
            'toDate' : component.get('v.toDate'),
            'offset' : 0,
            'pageSize' : 0,
            'filters' : JSON.stringify(component.get('v.filteredDataList'))
        });
        action.setCallback(this, function(response) {
            if (response.getState() === "SUCCESS") {
                // set ContactList list with return value from server.
                let dataJson = response.getReturnValue();
                component.set("v.dataObj", dataJson);
                component.set("v.tableData", dataJson.dataRowList);
                if(dataJson != undefined) {
                    component.set("v.tableColumns", dataJson.columnList);
                    component.set("v.showCheckbox", dataJson.showCheckbox);
                    component.set("v.allowSearch", dataJson.showSearch);
                    component.set("v.allowFilter", dataJson.isFilterable);
                    component.set("v.editable", dataJson.isEditable);
                    component.set("v.title", dataJson.title);
                    
                    component.set("v.allDataList",dataJson.allDataList);
                    component.set("v.filteredDataList",dataJson.filters);
                    component.set("v.componentName",dataJson.componentName);

                    component.set("v.pageSize", dataJson.recordPerPage);
                    component.set("v.pageNumber", 1);
                    component.set("v.start",0);
                    component.set("v.totalSize", dataJson.count);
                    component.set("v.totalPages", Math.ceil(dataJson.count/component.get("v.pageSize")));
                    component.set("v.end",component.get("v.totalPages")-1);
                    if(component.get("v.totalPages") == 0){
                        component.set("v.totalPages", 1);
                    }
                    component.set("v.offset",0);
                    component.set("v.tableData", dataJson.dataRowList);
					let recordId = dataJson.allDataList[0] != undefined ? dataJson.allDataList[0].key : undefined;
                    var eventObj = {};
                    eventObj.currentTarget = {};
                    eventObj.currentTarget.id = recordId;
                    component.set("v.activeRow",recordId);
                    if(component.get('v.is_employee')){
                        component.find('completedEmployeeTrainning').initialize();
                    }else if(component.get('v.is_course')){
                        component.find('completedTrainning').initialize();
                    }
                    
                    
                }
                
                component.set("v.selectedRows",[]);
                
            } else {
            }
            component.set('v.loaded', true);
        });
        $A.enqueueAction(action);
    },
})