<aura:component controller="DT_TableController" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
    access="global">
    <aura:handler name="init" value="{!this}" action="{!c.initRecords}" />
    <aura:attribute name="sortcolumn" type="String" />
    <aura:attribute name="loaded" type="boolean" default="false" />
    <aura:attribute name="filteredDataList" type="Object[]" />
    <aura:attribute name="sortdirectionasc" type="boolean" default="false" />
    <aura:attribute name="recordId" type="String" access="public" />
    <aura:attribute name="fromDate" type="Date" />
    <aura:attribute name="toDate" type="Date" />
    <aura:attribute name="tableData" type="Object[]" />
    <aura:attribute name="tableColumns" type="Object[]" />
    <aura:attribute name="showCheckbox" type="boolean" default="false" />
    <aura:attribute name="allowFilter" type="boolean" default="false" />
    <aura:attribute name="allowSearch" type="boolean" default="false" />
    <aura:attribute name="dataObj" type="Object" />
    <aura:attribute name="headerCheckbox" type="boolean" default="false" />
    <aura:attribute name="selectedRows" type="String[]" />
    <aura:attribute name="activeRow" type="String" default="" />
    <aura:attribute name="title" type="String" />
    <aura:attribute name="alloctionPassThrow" type="boolean" default="false" />
    <aura:attribute type="Boolean" name="editable" access="public" default="false" />
    <aura:attribute name="allDataList" type="Object[]" />
    <aura:attribute name="componentName" type="String" default="" />

    <aura:attribute name="pageSize" type="Integer" default="10" access="public" />
    <aura:attribute name="offset" type="Integer" default="0" />
    <aura:attribute name="totalSize" type="Integer" />
    <aura:attribute name="start" type="Integer" />
    <aura:attribute name="end" type="Integer" />
    <aura:attribute name="totalPages" type="Integer" />
    <aura:attribute name="pageNumber" type="Integer" default="1" />
    <aura:attribute name="controlPrevious" type="string" />
    <aura:attribute name="controlNext" type="string" />

    <aura:attribute name="is_filtered" type="boolean" default="false" />
    <aura:attribute name="is_employee" type="boolean" default="false" />
    <aura:attribute name="is_course" type="boolean" default="false" />
    <aura:attribute name="selectedName" type="String" default="" />
    <aura:attribute name="currentUserName" type="String" default="" />
    <div>
        <c:cmtTrainingMatrix class='stickyCss' onactioncall="{!c.actionHandler}" />
        <aura:if isTrue="{!v.is_filtered == true}">
            <aura:if isTrue="{!v.is_employee}">
                <c:DT_Table aura:id="completedEmployeeTrainning" listname="My_Completed_Training_Employee" showCompletedTrainingActionBtn="true" recordId="{!v.recordId}" appendName="{!v.selectedName}"></c:DT_Table>
            </aura:if>
            <aura:if isTrue="{!v.is_course}">
                <c:DT_Table aura:id="completedTrainning" listname="Completed_Training" showCompletedTrainingActionBtn="true" recordId="{!v.recordId}" appendName="{!v.selectedName}"></c:DT_Table>
            </aura:if>
        </aura:if>
        <aura:if isTrue="{!AND(v.is_filtered == false,v.currentUserName !='')}">
            <c:DT_Table aura:id="allcompletedTrainning" listname="My_Completed_Training_Employee" showCompletedTrainingActionBtn="true" recordId="{!v.recordId}" appendName="{!v.currentUserName}"></c:DT_Table>
        </aura:if>

    </div>
</aura:component>