({
	initRecords : function(component, event, helper) {
        
	var names = component.get('c.getCurrtentUser');
            names.setCallback(this,function(response) { 
                var nameFetched = response.getReturnValue();
                console.log('namefetched',nameFetched);
                component.set('v.currentUserName',nameFetched);
            });
	$A.get('e.force:refreshView').fire();
        $A.enqueueAction(names);
        
	},
    
    actionHandler : function(component,event,helper){
        var selectedId = event.getParam('value');
        var selectedName = event.getParam('name');
        var selectedElement = event.getParam('element_name');
        
        if(selectedElement == 'Employee'){
            component.set('v.is_employee',true)
            component.set('v.is_course',false)
        }else if(selectedElement == 'Course'){
            component.set('v.is_employee',false)
            component.set('v.is_course',true)
        }
        component.set('v.recordId',selectedId); 
        component.set('v.selectedName',selectedName);
        component.set('v.is_filtered',true)
        helper.refreshTable(component, event, helper, component.get('v.sortcolumn'));
    },
})
