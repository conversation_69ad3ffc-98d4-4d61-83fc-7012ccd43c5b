<aura:component controller="CMT_NCDashboardCtrl"
    implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction"
    access="global">
    <!-- <aura:handler name="init" value="{!this}" action="{!c.initRecords}"/>     -->
    <aura:attribute name="ViewName" type="String" default="Admin" />
    <aura:attribute name="TabName" access="global" type="String" default="NON CONFORMITY" />
    <div>
        <div class="dashboard">
            <div class="notification">
                <aura:if isTrue="{!v.TabName == 'NON CONFORMITY'}">
                    <c:cmtDashboardStatus modulename="nc" viewname="{!v.ViewName}" onactioncall="{!c.actionHandler}"
                        viewId="11" />
                </aura:if>
            </div>
            <aura:if isTrue="{!v.TabName == 'NON CONFORMITY'}">
                <aura:if isTrue="{!v.ViewName == 'Admin'}">
                    <c:DT_Table listname="Non_Confirmity_Log" showNCEditAction="true" showNLButton="true" />
                </aura:if>
                <!--Admin-->
                <aura:if isTrue="{!v.ViewName == 'CloseNonConfirm'}">
                    <c:DT_Table listname="Closed_Non_Confirmity_Log" />
                </aura:if>
                <!--Owner View -->
            </aura:if>
            <aura:if isTrue="{!v.TabName == 'CORRECTIVE ACTION'}">
                <aura:if isTrue="{!v.ViewName == 'Admin'}">
                    <c:DT_Table listname="Corrective_Action_Log" showCALButton="true" showCAEditAction="true" />
                </aura:if>
                <!--Approver View -->
                <aura:if isTrue="{!or(v.ViewName == 'Owner',v.ViewName == 'Approver')}">
                    <c:DT_Table listname="Corrective_Action_Log" />
                </aura:if>
                <!--Owner View -->
            </aura:if>
        </div>
    </div>
</aura:component>