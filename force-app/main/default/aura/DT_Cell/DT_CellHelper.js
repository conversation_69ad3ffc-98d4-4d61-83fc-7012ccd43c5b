({
	onCellChangeJSHelper : function (component, event, helper) {
        var p = component.get("v.onCellChange");
        $A.enqueueAction(p);
    },

    loadComponentAttribute : function(cmp, cmpname) {
        if(cmpname == 'c:cmtWorkOrderButtons') {
            cmp.set('v.showWorkOrderModal',true);
            cmp.handleLoad();
        } else if(cmpname == 'c:cmtAssetButtons') {
            cmp.set('v.showNewAssetButton',true);
            cmp.handleLoad();
        } else if(cmpname == 'c:cmtAuditChecklist') {
            cmp.handleLoad();
        }
    }
})