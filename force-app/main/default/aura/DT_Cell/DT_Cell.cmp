<aura:component implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,force:lightningQuickAction" access="global">
    <!--declare aura attributes-->
    <aura:attribute name="showSaveCancelBtn" type="boolean" />
    <aura:attribute name="showErrorClass" type="boolean" default="false" />
    <aura:attribute name="label" type="String" />
    <aura:attribute name="cellValue" type="Object" />
    <aura:attribute name="columnName" type="String" />
    <aura:attribute name="columnAPIName" type="String" />
    <aura:attribute name="dataType" type="String" default="String" />
    <aura:attribute name="columnDataType" type="String" default="String" />
    <aura:attribute name="altValue" type="Object" />
    <aura:attribute name="altValueNumeric" type="Integer" />
    <aura:attribute name="EditMode" type="boolean" default="false" />
    <aura:attribute name="isEditable" type="boolean" default="false" />
    <aura:attribute name="isClickable" type="boolean" default="false" />
    <aura:attribute name="isComponent" type="boolean" default="false" />
    <aura:attribute name="rowId" type="string" default="" />
    <aura:attribute name="row" type="object" />
    <aura:attribute name="componentName" type="String" default="" />

    <aura:attribute name="onCellChange" type="Aura.action" />
    <aura:attribute name="parentAction" type="Aura.Action" />
    <aura:attribute name="cbody" type="Aura.Component" />

    <aura:attribute name="dateValidation" type="Boolean" />

    <aura:handler name="init" value="{!this}" action="{!c.handleLoad}" />

    <!--Table Row Start-->

    <td ondblclick="{!c.inlineEditName}" class="{! v.showErrorClass == true ? 'slds-cell-edit slds-has-error' : 'slds-cell-edit'}">
        <span class="slds-grid slds-grid_align-spread">
            <!-- show input and output section based on boolean flag -->
            <aura:if isTrue="{!v.cellValue != 'null'}">
                <aura:if isTrue="{!v.dataType == 'STRING'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <aura:if isTrue="{!v.isClickable == true}">
                            <span class="slds-truncate blackClass" title="{!v.cellValue}"><a href="{!'/'+v.rowId}">{!v.cellValue}</a></span>
                        </aura:if>
                        <aura:if isTrue="{!v.isComponent == true}">
                            <span class="slds-truncate" componentName="{!v.componentName}" title="{!v.cellValue}">{!v.cellValue}</span>
                        </aura:if>
                        <aura:if isTrue="{!and(v.isClickable != true,v.isComponent != true)}">
                            <span class="slds-truncate" title="{!v.cellValue}">
                                <aura:unescapedHtml value="{!v.cellValue}" />
                            </span>
                        </aura:if>
                        <aura:if isTrue="{!v.isEditable == true}">
                            <button onclick="{!c.inlineEditName}" class="slds-button slds-button_icon slds-cell-edit__button slds-m-left_x-small" tabindex="0" title="Edit LastName">
                                <lightning:icon iconName="utility:edit" size="xx-small" alternativeText="edit" />
                            </button>
                        </aura:if>
                        <!-- Inline Edit Section in else case-->
                        <aura:set attribute="else">
                            <section tabindex="0" class="slds-popover slds-popover_edit inputBoxCss" role="dialog">
                                <div class="slds-popover__body">
                                    <div class="slds-form-element slds-grid slds-wrap">
                                        <div class="slds-form-element__control slds-grow">
                                            <ui:inputText class="slds-input inputFieldWidth" labelClass="slds-form-element__label slds-form-element__label_edit slds-no-flex" aura:id="inputId" blur="{!c.closeNameBox}"
                                                change="{!c.onNameChange}" required="true" label="{!v.label}" value="{!v.cellValue}" />
                                        </div>
                                    </div>
                                </div>
                                <span id="form-end" tabindex="0"></span>
                            </section>
                        </aura:set>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'REFERENCE'}">

                    <aura:if isTrue="{!v.altValue != 'null'}">



                        <aura:if isTrue="{!v.EditMode == false}">
                            <aura:if isTrue="{!v.isClickable == true}">
                                <span class="slds-truncate" title="{!v.cellValue}"><a href="{!'/'+v.cellValue}">{!v.altValue}</a></span>
                            </aura:if>

                            <aura:if isTrue="{!v.isClickable != true}">
                                <aura:if isTrue="{!v.columnName != 'DPD'}">
                                    <aura:if isTrue="{!v.columnName == 'Corrective Action ID (If Any)'}">
                                        <span class="slds-truncate" title="{!v.altValue}"><a href="" onclick="{!c.openCAModal}">{!v.altValue}</a></span>
                                    </aura:if>
                                    <aura:if isTrue="{!v.columnName != 'Corrective Action ID (If Any)'}">
                                        <!--- all general reference field logic sit here -->
                                        <aura:if isTrue="{!v.columnDataType == 'TIME'}">
                                            <span class="slds-truncate" title="{!v.altValue}">
                                                <lightning:formattedTime value="{!v.altValue}" hour="2-digit" minute="2-digit" second="2-digit" hour12="true" timeZone="UTC" />
                                            </span>
                                            <aura:set attribute="else">
                                                <span class="slds-truncate" title="{!v.altValue}">{!v.altValue}</span>
                                            </aura:set>
                                        </aura:if>
                                        <!-- <aura:if isTrue="{!v.columnDataType != 'TIME'}">
                                            <span class="slds-truncate" title="{!v.altValue}">{!v.altValue}</span>
                                        </aura:if> -->
                                    </aura:if>

                                </aura:if>
                                <aura:if isTrue="{!v.columnName == 'DPD'}">
                                    <aura:if isTrue="{!lessthanorequal(v.altValue,3)}">
                                        <span class="slds-truncate blueStyle" title="{!v.altValue}">{!v.altValue}</span>
                                    </aura:if>
                                    <aura:if isTrue="{!and(greaterthanorequal(v.altValue,8),lessthanorequal(v.altValue,16))}">
                                        <span class="slds-truncate orangeStyle" title="{!v.altValue}">{!v.altValue}</span>
                                    </aura:if>
                                    <aura:if isTrue="{!greaterthanorequal(v.altValue,17)}">
                                        <span class="slds-truncate dueDateStyle" title="{!v.altValue}">{!v.altValue}</span>
                                    </aura:if>
                                </aura:if>
                            </aura:if>
                        </aura:if>


                    </aura:if>
                </aura:if>





                <aura:if isTrue="{!v.dataType == 'DATE'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <span class="slds-truncate" title="{!v.cellValue}">
                            <aura:if isTrue="{!or(v.columnAPIName == 'Due_Date__c', v.columnAPIName == 'AADate_of_Request__c')}">
                                <aura:if isTrue="{!v.dateValidation}">
                                    <lightning:formattedDateTime value="{!v.cellValue}" year="numeric" month="numeric" day="numeric" timeZone="Asia/Kolkata" class="dueDateStyle" />
                                    <aura:set attribute="else">
                                        <lightning:formattedDateTime value="{!v.cellValue}" year="numeric" month="numeric" day="numeric" timeZone="Asia/Kolkata" />
                                    </aura:set>
                                </aura:if>
                            </aura:if>
                            <aura:if isTrue="{!and(v.columnAPIName != 'Due_Date__c', v.columnAPIName != 'ADate_of_Request__c')}">
                                <lightning:formattedDateTime value="{!v.cellValue}" year="numeric" month="numeric" day="numeric" timeZone="Asia/Kolkata" />
                            </aura:if>
                            <!--  <lightning:formattedDateTime value="{!v.cellValue}" month="2-digit" year="2-digit" day="2-digit" timeZone="Asia/Kolkata"/> -->
                        </span>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'TIME'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <span class="slds-truncate" title="{!v.cellValue}">
                            <lightning:formattedDateTime value="{!v.cellValue}" hour="2-digit" minute="2-digit" second="2-digit" hour12="true" timeZone="UTC" />
                        </span>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'PICKLIST'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <aura:if isTrue="{!or(v.columnAPIName == 'Approval_Status__c',or(v.columnAPIName == 'Status__c',v.columnAPIName == 'Author_Status__c'))}">
                            <!-- <aura:if isTrue="{!v.cellValue == 'Approved'}">
                                    <span class="slds-truncate approved" title="{!v.cellValue}">{!v.cellValue}</span>
                                </aura:if>
                                <aura:if isTrue="{!or(v.cellValue == 'Pending Approval',v.cellValue == 'Rejected')}">
                                    <span class="slds-truncate rejected" title="{!v.cellValue}">{!v.cellValue}</span>
                                </aura:if> -->
                            <span class="slds-truncate" title="{!v.cellValue}">{!v.cellValue}</span>
                        </aura:if>
                        <aura:if isTrue="{!and(v.columnAPIName != 'Approval_Status__c',v.columnAPIName != 'Status__c',v.columnAPIName != 'Author_Status__c')}">
                            <span class="slds-truncate" title="{!v.cellValue}">{!v.cellValue}</span>
                        </aura:if>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'DATETIME'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <span class="slds-truncate" title="{!v.cellValue}">
                            <lightning:formattedDateTime value="{!v.cellValue}" month="2-digit" year="2-digit" day="2-digit" hour="2-digit" minute="2-digit" timeZone="Asia/Kolkata" />
                        </span>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'CURRENCY'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <lightning:formattedNumber value="{!v.cellValue}" style="currency" currencyCode="INR" minimumFractionDigits="0" maximumFractionDigits="0" />
                        <aura:if isTrue="{!v.isEditable == true}">
                            <button onclick="{!c.inlineEditName}" class="slds-button slds-button_icon slds-cell-edit__button slds-m-left_x-small" tabindex="0" title="Edit LastName">
                                <lightning:icon iconName="utility:edit" size="xx-small" alternativeText="edit" />
                            </button>
                        </aura:if>
                        <aura:set attribute="else">
                            <section tabindex="0" class="slds-popover slds-popover_editinputBoxCss" role="dialog">
                                <div class="slds-popover__body">
                                    <div class="slds-form-element slds-grid slds-wrap">
                                        <div class="slds-form-element__control slds-grow">
                                            <lightning:input type="number" name="{!v.row.columnAPIName}" label="Enter a number" aura:id="inputId" variant="label-hidden" value="{!v.cellValue}" onchange="{!c.onNameChange}"
                                                onblur="{!c.closeNameBox}" />
                                        </div>
                                    </div>
                                </div>
                                <span id="form-end" tabindex="0"></span>
                            </section>
                        </aura:set>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'DOUBLE'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <lightning:formattedNumber value="{!v.cellValue}" />
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'PERCENT'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <aura:if isTrue="{!v.cellValue != null}">
                            <span class="slds-truncate" title="{!v.cellValue}">{!v.cellValue}%</span>
                        </aura:if>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'TEXTAREA'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <span class="slds-truncate" title="{!v.cellValue}">{!v.cellValue}</span>
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'BOOLEAN'}">
                    <aura:if isTrue="{!v.EditMode == false}">
                        <span class="slds-truncate">
                            <lightning:input name="{!v.rowId}" type="checkbox" checked="{!v.cellValue}" disabled="true" class="slds-align_absolute-center" />
                        </span>
                    </aura:if>
                    <aura:if isTrue="{!v.EditMode == true}">
                        <lightning:input aura:id="{!v.rowId}" name="{!v.rowId}" value="{!v.rowId}" type="checkbox" checked="{!v.cellValue}" />
                    </aura:if>
                </aura:if>
                <aura:if isTrue="{!v.dataType == 'CHECKBOX'}">
                    <aura:if isTrue="{!v.isEditable == false}">
                        <lightning:input name="{!v.rowId}" type="checkbox" checked="{!v.cellValue}" disabled="true" />
                    </aura:if>
                    <aura:if isTrue="{!v.isEditable == true}">
                        <lightning:input aura:id="{!v.rowId}" onchange="{!c.onCellChangeJS}" name="{!v.rowId}" value="{!v.cellValue}" type="checkbox" checked="{!v.cellValue}" />
                    </aura:if>
                </aura:if>
            </aura:if>
        </span>
        <div aura:id="placeholder">
            {!v.cbody}
        </div>
    </td>
    <!---->
</aura:component>