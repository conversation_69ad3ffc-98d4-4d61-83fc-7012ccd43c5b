({
    inlineEditName: function (component, event, helper) {

        if (component.get('v.isEditable') == true) {
            component.set("v.EditMode", true);
            setTimeout(function () {
                //component.find("inputId").focus();
            }, 100);
        }
    },

    handleLoad: function (component, event, helper) {
        if (component.get("v.dataType") == 'DATE') {
            var d = new Date(),
                month = '' + (d.getMonth() + 1),
                day = '' + d.getDate(),
                year = d.getFullYear();
            if (month.length < 2) month = '0' + month;
            if (day.length < 2) day = '0' + day;
            var row = component.get("v.row");
            let closedDate;
            for (let key in row.dataTableRecordList) {
                if (row.dataTableRecordList[key].columnAPIName == 'Closed_Date__c') {
                    closedDate = row.dataTableRecordList[key].columnValue;
                }
            }
            if (closedDate != undefined) {
                component.set("v.dateValidation", false);
            } else {
                component.set("v.dateValidation", [year, month, day].join('-') > component.get("v.cellValue"));
            }
        }
    },

    onNameChange: function (component, event, helper) {
        // if edit field value changed and field not equal to blank,
        // then show save and cancel button by set attribute to true
        if (event.getSource().get("v.value") != undefined && event.getSource().get("v.value").trim() != '') {
            component.set("v.showSaveCancelBtn", true);
            component.set("v.row.isSelected", true);
            helper.onCellChangeJSHelper(component, event, helper);
        }
    },

    closeNameBox: function (component, event, helper) {
        // on focus out, close the input section by setting the 'nameEditMode' att. as false   
        component.set("v.EditMode", false);
        // check if change/update Name field is blank, then add error class to column -
        // by setting the 'showErrorClass' att. as True , else remove error class by setting it False   
        if (event.getSource().get("v.value") != undefined && event.getSource().get("v.value").trim() == '') {
            component.set("v.showErrorClass", true);
        } else {
            component.set("v.showErrorClass", false);
        }
    },

    onCellChangeJS: function (component, event, helper) {
        helper.onCellChangeJSHelper(component, event, helper);
    },

    openCAModal: function (component, event, helper) {
        var parentComponent = component.get("v.parentAction");
        parentComponent.openCApreview(component.get('v.cellValue'))

    },

    loadComponent: function (component, event, helper) {
        const recordId = component.get('v.rowId');
        const componentName = component.get('v.componentName');
        $A.createComponent(
            componentName, { recordId: recordId },
            function (lwcCmp, status, errorMessage) {
                if (status === "SUCCESS") {
                    //var placeholder = component.find('placeholder');
                    component.set("v.cbody", lwcCmp);
                    /*
                    var body = component.get("v.body");
                    body.push(lwcCmp);
                    component.set("v.body", body);
                    */
                    helper.loadComponentAttribute(lwcCmp, componentName);
                }
                else if (status === "INCOMPLETE") {
                }
                else if (status === "ERROR") {
                }
            }
        );
    }
})