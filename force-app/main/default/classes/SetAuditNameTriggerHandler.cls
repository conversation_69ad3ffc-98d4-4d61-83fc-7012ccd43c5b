public with sharing class SetAuditNameTriggerHandler {
    
    public static void changeName(List<Audit_Calendar__c> newAuditCalendarList)
    {
        try{
        List<Audit_Calendar__c> objectlst = new List<Audit_Calendar__c>();
        if(AccessibilityManager.isAccessible('Audit_Calendar__c')) {
            objectlst = [select id,Name,Name__c,Account__c from Audit_Calendar__c where Account__c =: CMT_AuditConfiguratorLWCCtrl.getAccountId() order by CreatedDate desc limit 1];
        }
        Integer lastObjectNo = 0;
        if(objectlst.size() != 0 && objectlst[0].Name__c != null){
            List<String> Name_ary = objectlst[0].Name__c.split('-');
            if(Name_ary.size() > 1){
                lastObjectNo = Integer.valueOf(Name_ary[1]);
            }
        }
        for(Audit_Calendar__c ob : newAuditCalendarList){
            lastObjectNo = lastObjectNo + 1;
            ob.Name__c = 'AUD-' + String.valueOf(lastObjectNo).leftPad(5, '0');
        }
        
    }catch(Exception e){
        
    }
    }

}