@IsTest
private class DocumentTriggerHandlerTest {

	@TestSetup
	private static void testSetup() {
		Account acc = new Account(Name = 'Account');
		insert acc;

		insert new List<Document__c> {
			new Document__c(Document_Title__c = 'Title 1', Account__c = acc.Id),
			new Document__c(Document_Title__c = 'Title 2', Account__c = acc.Id)
		};
	}
	@IsTest
	private static void testInsertValidationUniqueTitle() {
		Account acc = [SELECT Id FROM Account LIMIT 1];

		List<Document__c> documents = new List<Document__c> {
			new Document__c(Document_Title__c = 'Title 1', Account__c = acc.Id),
			new Document__c(Document_Title__c = 'Title 3', Account__c = acc.Id)
		};

		System.Test.startTest();
		List<Database.SaveResult> saveResults = Database.insert(documents, false);
		System.Test.stopTest();

		Assert.areEqual(2, saveResults.size(), 'There are 2 Documents on insert.');
		Assert.isFalse(saveResults.get(0).isSuccess(), 'First Document has duplicate value "Title 1".');
		Assert.areEqual('The document title already exists in another record!', saveResults.get(0).getErrors().get(0).getMessage());
		Assert.isTrue(saveResults.get(1).isSuccess(), JSON.serialize(saveResults.get(1).getErrors()));
	}

	@IsTest
	private static void testUpdateValidationUniqueTitle() {
		Document__c document1 = [SELECT Id FROM Document__c WHERE Document_Title__c = 'Title 1' LIMIT 1];

		System.Test.startTest();
		document1.Document_Title__c = 'Title 2';
		Database.SaveResult saveResult = Database.update(document1, false);

		System.Test.stopTest();

		Assert.isFalse(saveResult.isSuccess(), 'Document has duplicate value "Title 1".');
		Assert.areEqual('The document title already exists in another record!', saveResult.getErrors().get(0).getMessage());
	}
}