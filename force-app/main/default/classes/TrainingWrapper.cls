public with sharing class TrainingWrapper {
    @auraenabled
    public String trainingId {get;set;}
    @auraenabled
    public String trainingName {get;set;}
    @auraenabled
    public String type {get;set;}
    @auraenabled
    public String trainer {get;set;}
    @auraenabled
    public String trainingDate {get;set;}
    @auraenabled
    public List<EmployeeTrainingWrapper> empTrainingLst {get;set;}
    public TrainingWrapper(){
        empTrainingLst = new List<EmployeeTrainingWrapper>();
    }
}