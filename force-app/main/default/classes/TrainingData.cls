public with sharing class TrainingData {
    @auraenabled
    public String trainingId {get;set;} //Meeting Name
    @auraenabled
    public String trainingName {get;set;}
    @auraenabled
    public String type {get;set;}
    @auraenabled
    public String trainer {get;set;}
    @auraenabled
    public String trainerEmailAddress {get;set;}
    @auraenabled
    public String trainingDate {get;set;}
    @auraenabled
    public String trainingTime {get;set;}
    @auraenabled
    public String endDate {get;set;}
    @auraenabled
    public String endTime {get;set;}
    @auraenabled
    public Boolean isMandatory {get;set;}
    @auraenabled
    public Double duration {get;set;}
    @auraenabled
    public String department {get;set;}
    @auraenabled
    public String supervisor {get;set;}
    @auraenabled
    public String comments {get;set;}
    @auraenabled
    public String Provider {get;set;}
    @auraenabled
    public Boolean isGroupTraining {get;set;}
    @auraenabled
    public String status {get;set;}
    @auraenabled
    public String course {get;set;}
    @auraenabled
    public String course_name {get;set;}
    
    @auraenabled
    public String filebody {get;set;}
    @auraenabled
    public String fileType {get;set;}
    @auraenabled
    public String fileName {get;set;}
    @auraenabled
    public String fileBaseUrl {get;set;} //To download the file uploaded
    @auraenabled
    public List<EmployeeData> employeeList  {get;set;}
    @auraenabled
    public String comment {get;set;}
    @auraEnabled
    public Boolean trainingeffective {get;set;}
    @auraEnabled
    public Boolean closeTraining {get;set;}
    @auraEnabled
    public String feedback {get;set;}
    @auraEnabled
    public String employeeId {get;set;}
    @auraEnabled
    public String Emploeenames {get;set;}
    @auraEnabled
    public String trainingFormType {get;set;}
    @auraEnabled
    public String employeeTrainingId {get;set;}
    @auraEnabled
    public String trainingManagementId {get;set;}

    // CC-525
    @auraEnabled
    public String verificationTimeline {get;set;}
    @auraEnabled
    public String verificationMethod {get;set;}
    @auraEnabled
    public String verificationResponsible {get;set;}
    @auraenabled
    public String verificationDate {get;set;}
    @auraenabled
    public String verifiedBy {get;set;}
    @auraEnabled
    public decimal cost {get;set;}
    @auraEnabled
    public Boolean isFree {get;set;}
    @auraEnabled
    public Boolean isExternal {get;set;}
    @auraEnabled
    public String format {get;set;}
    @auraEnabled
    public String topic {get;set;}
    @auraEnabled
    public String locationId {get;set;}
    @auraEnabled
    public String additionalApprover {get;set;}
    @auraEnabled
    public String trainingTitle {get;set;}
    @auraEnabled
    public String trainingType {get;set;}
    @auraEnabled
    public String jobTitles {get;set;}
    @auraEnabled
    public String mandatoryDepartment {get;set;}
    @auraEnabled
    public String trainingFor {get;set;}
    @auraEnabled
    public String employees {get;set;}
    @auraEnabled
    public List<PicklistValue> employeesList {get;set;}
    @auraenabled
    public String dueDate {get;set;}
    @auraenabled
    public String scheduledTime {get;set;}
    @auraenabled
    public String completedTime {get;set;}
    @auraenabled
    public Double daysBeforeNextOccurrence {get;set;}
    @auraenabled
    public String statusOwner {get;set;}
    @auraenabled
    public String description {get;set;}
    @auraenabled
    public String trainees {get;set;}
    @auraenabled
    public String userTrainees {get;set;}
    @auraenabled
    public String userSupervisors {get;set;}
    @auraEnabled
    public String feedbackDate {get;set;}
    @auraEnabled
    public String feedbackBy {get;set;}
    @auraEnabled
    public String feedbackComment {get;set;}
    @auraEnabled
    public String feedbackValue {get;set;}
    @auraEnabled
    public String frequency {get;set;}
    @auraEnabled
    public String courseLink {get;set;}
    @auraEnabled
    public String jobTitlesForTraining {get;set;}
    @auraEnabled
    public String deptForTraining {get;set;}
    @auraEnabled
    public String sitesForTraining {get;set;}
    @auraEnabled
    public List<Employee_Training__c> employeeTrainings {get;set;}
    @auraEnabled
    public List<cocmd__Training_Feedback__c> trainingFeedbacks {get;set;}
    
    
    public TrainingData(){
        employeeList = new List<EmployeeData>();
        this.filebody = fileType = fileName = fileBaseUrl = '' ;
    }
    public TrainingData(String trainingId, String trainingName, String type, String trainer, String trainingDate, 
                        String trainingTime, Boolean isMandatory, Double duration, String department, 
                        String supervisor, String comments,String endDate,String endTime,String Provider,String status,boolean trainingeffective){
                            this.trainingId = trainingId;
                            this.trainingName = trainingName;
                            this.type = type;
                            this.trainer = trainer;
                            this.trainingDate = trainingDate;
                            this.trainingTime = trainingTime;
                            this.isMandatory = isMandatory;
                            this.duration = duration;
                            this.department = department;
                            this.supervisor = supervisor;
                            this.comments = comments;
                            this.endDate = endDate;
                            this.endTime = endTime;
                            this.Provider = Provider;
                            this.status = status;
                            this.trainingeffective = trainingeffective;
                            this.closeTraining = false;
                        }
}