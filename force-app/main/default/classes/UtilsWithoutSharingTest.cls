@isTest
public class UtilsWithoutSharingTest {

    @Future public static void testSetup(){        
        TestDataFactory.createAccounts(1);
        Account a = [Select id,name from Account LIMIT 1];
        List<Contact> Lstcon = TestDataFactory.craeteContact(1,a.Id);
        Contact con = [Select id,AccountId from Contact where id=:Lstcon[0].id];
        Profile p = [select Id, name from Profile where Name in ('Customer Community Plus User') limit 1];

        User newUser = new User(
            profileId = p.Id,
            username = 'newUser'+'Test'+'@yahoo.com',
            email = '<EMAIL>',
            emailencodingkey = 'UTF-8',
            localesidkey = 'en_US',
            languagelocalekey = 'en_US',
            timezonesidkey = 'America/Los_Angeles',
            alias='nuser',
            lastname='lastname');
        newUser.contactID = con.id;
        Insert newUser;
    }


    @isTest
    static void testGetInProgressTaskCount() {
        // Create test data
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        Task testTask1 = new Task(Subject = 'Test Task 1', Status = 'Open', WhatId = testAccount.Id);
        Task testTask2 = new Task(Subject = 'Test Task 2', Status = 'Closed', WhatId = testAccount.Id);
        insert new List<Task>{testTask1, testTask2};
        
        // Test the method
        Integer result = UtilsWithoutSharing.getInProgressTaskCount(testAccount.Id);
        System.assertEquals(1, result);
    }
    
    @isTest
    static void testReopenAllTasksByRecordId() {
        // Create test data
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;
        
        Task testTask1 = new Task(Subject = 'Test Task 1', Status = 'Closed', WhatId = testAccount.Id);
        Task testTask2 = new Task(Subject = 'Test Task 2', Status = 'Open', WhatId = testAccount.Id);
        insert new List<Task>{testTask1, testTask2};
        
        // Test the method
        Test.startTest();
        UtilsWithoutSharing.reopenAllTasksByRecordId(testAccount.Id);
        Test.stopTest();
        
        List<Task> updatedTasks = [SELECT Status FROM Task WHERE WhatId = :testAccount.Id];
        for (Task task : updatedTasks) {
            System.assertEquals('Open', task.Status);
        }
    }   
 @isTest
static void testShareDocumentsWithRelaventUsers() {
    testSetup();
        
    Test.startTest();
    Test.stopTest();

    // Create a set of user Ids
    Set<Id> userIds = new Set<Id>();
    List<User> userList = [SELECT Id FROM User WHERE Id != :UserInfo.getUserId() AND Profile.Name = 'Customer Community Plus User' LIMIT 1];
    for (User us : userList) {
        userIds.add(us.Id);
    }
    
    // Create test data
    
    Id networkId = [SELECT Id FROM Network LIMIT 1].Id;

    ContentVersion testContentVersion = new ContentVersion(
        Title = 'Test Document',
        PathOnClient = 'testdocument.txt',
        VersionData = Blob.valueOf('Test content'),
        NetworkId = networkId
    );
    insert testContentVersion;
    
    // Retrieve the ContentDocumentId from the inserted ContentVersion
    List<ContentVersion> conList = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :testContentVersion.Id];
    System.assertEquals(1, conList.size(), 'Expected one ContentVersion record');
    
    // Create a test ContentDocumentLink
    ContentDocumentLink testContentLink = new ContentDocumentLink(
        ShareType = 'V', 
        Visibility = 'AllUsers',
        ContentDocumentId = conList[0].ContentDocumentId,
        LinkedEntityId = userList[0].Id
    );
    insert testContentLink;

	ContentDocument contentDocuments = [select Id, OwnerId from ContentDocument Where Id =:conList[0].ContentDocumentId];
    contentDocuments.OwnerId = userList[0].Id;
    Update contentDocuments;
    // Test the method
    
    try{
        UtilsWithoutSharing.shareDocumentsWithRelaventUsers(conList[0].ContentDocumentId, userIds);
    } catch(Exception e){}

    // Verify the ContentDocumentLink records
    List<ContentDocumentLink> links = [SELECT LinkedEntityId FROM ContentDocumentLink WHERE ContentDocumentId = :conList[0].ContentDocumentId];
    //System.assertEquals(11, links.size(), 'Expected 11 ContentDocumentLink records'); // 1 existing + 10 new links
}

}