public with sharing class SetMeetingNameTriggerHandler {
    

    
    public static void changename(List<Management_Review__c> newManagmentList)
    {
        try{
        List<Management_Review__c> objectlst = new List<Management_Review__c>();
        if(AccessibilityManager.isAccessible('Management_Review__c')) {
            objectlst =[select id,Name,Name__c,Account__c from Management_Review__c where Account__c =: CMT_AuditConfiguratorLWCCtrl.getAccountId() order by CreatedDate desc limit 1];
        }
        Integer lastObjectNo = 0;
        if(objectlst.size() != 0 && objectlst[0].Name__c != null){
            List<String> Name_ary = objectlst[0].Name__c.split('-');
            if(Name_ary.size() > 1){
                lastObjectNo = Integer.valueOf(Name_ary[1]);
            }
        }
        for(Management_Review__c ob : newManagmentList){
            lastObjectNo = lastObjectNo + 1;
            ob.Name__c = 'M-' + String.valueOf(lastObjectNo).leftPad(5, '0');
        }
        
    }catch(Exception e){
        
    }
    }

}