@isTest
public class SetAssetNameTriggerTest {

 @Future public static void testSetup(){
       
        TestDataFactory.createAccounts(1);
        
        Account a = [Select id,name from Account LIMIT 1];
       
        List<Contact> Lstcon = TestDataFactory.craeteContact(1,a.Id);
        Contact con = [Select id,AccountId from Contact where id=:Lstcon[0].id];
        
        Profile p = [select Id,name from Profile where Name in ('Customer Community User') limit 1];
        User newUser = new User(
            profileId = p.id,
            username = 'newUser'+'Test'+'@yahoo.com',
            email = '<EMAIL>',
            emailencodingkey = 'UTF-8',
            localesidkey = 'en_US',
            languagelocalekey = 'en_US',
            timezonesidkey = 'America/Los_Angeles',
            alias='nuser',
            lastname='lastname');
        newUser.contactID = con.id;
        Insert newUser;
        TestDataFactory.createDepartments(1);
     TestDataFactory.createAssets(1);
        
    }    
    @isTest 
    public static void beforeTest() {
         testSetup();
        
        Test.startTest();
        Test.stopTest();
        
        User newUser = [Select id,ProfileId,contact.AccountId from User ORDER BY CreatedDate DESC limit 1];
        System.assertEquals(true, newUser != null);
        PermissionSet psObj = [SELECT ID, Name FROM PermissionSet WHERE Name = 'Customer_Community_User_Access_To_Custom_Objects'];
        PermissionSetAssignment psaObj = new PermissionSetAssignment(
            PermissionSetId = psObj.Id,
            AssigneeId = newUser.Id
        );
        insert psaObj;
        
        
        
        System.runAs(newUser) {
        List<Training_Skill__c> trSkillList = [Select id,name from Training_Skill__c];
        List<Department__c> dptList = [Select id,name from Department__c];
        List<Asset__c> trLst = new List<Asset__c>();
            Asset__c tr = new Asset__c ( account__c = newUser.contact.AccountId, Name ='test-Test2');
            trLst.add(tr);
        insert trLst;  
        }
    }

}