public with sharing class SetTrainingNameTriggerHandler {

public static void changeName(List<Training_Management__c> newTrainingList)
{
    try{
        List<Training_Management__c> objectlst =new  List<Training_Management__c>();
        if(AccessibilityManager.isAccessible('Training_Management__c'))
        objectlst = [select id,Name,Name__c,Account__c from Training_Management__c where Account__c =: CMT_AuditConfiguratorLWCCtrl.getAccountId() order by CreatedDate desc limit 1];
   
        Integer lastObjectNo = 0;
    if(objectlst.size() != 0 && objectlst[0].Name__c != null){
        List<String> Name_ary = objectlst[0].Name__c.split('-');
        if(Name_ary.size() > 1){
            lastObjectNo = Integer.valueOf(Name_ary[1]);
        }
    }
    for(Training_Management__c ob : newTrainingList){
        lastObjectNo = lastObjectNo + 1;
        ob.Name__c = 'T-' + String.valueOf(lastObjectNo).leftPad(5, '0');
    }
    
}catch(Exception e){
    
}
}

}