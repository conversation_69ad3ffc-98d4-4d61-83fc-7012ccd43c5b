public with sharing class Document<PERSON>riggerHandler {
	public static void onBeforeInsert(List<Document__c> records) {
		validateUniqueTitle(records);
	}

	public static void onBeforeUpdate(List<Document__c> records, Map<Id, Document__c> oldRecords) {
		List<Document__c> documents = new List<Document__c>();
		for (Document__c record : records) {
			Document__c oldRecord = oldRecords.get(record.Id);
			if (record.Document_Title__c != oldRecord.Document_Title__c) {
				documents.add(record);
			}
		}

		if ( ! documents.isEmpty()) {
			validateUniqueTitle(documents);
		}
	}

	private static void validateUniqueTitle(List<Document__c> documents) {
		List<String> titles = new List<String>();
		for (Document__c record : documents) {
			titles.add(record.Document_Title__c);
		}

		Set<String> existingTitles = new Set<String>();
		List<AggregateResult> results = [
			SELECT Document_Title__c title
			FROM Document__c
			WHERE Document_Title__c IN :titles
			AND Document_Status__c != 'Inactive'
			GROUP BY Document_Title__c
			LIMIT 10000
		];

		for (AggregateResult result : results) {
			existingTitles.add((String)result.get('title'));
		}

		for (Document__c record : documents) {
			if (existingTitles.contains(record.Document_Title__c)) {
				record.addError('The document title already exists in another record!');
			}
		}
	}
}