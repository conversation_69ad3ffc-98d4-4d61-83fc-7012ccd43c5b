public with sharing class <PERSON><PERSON><PERSON><PERSON><PERSON>lass {
    @auraEnabled 
    public List<Document_Change_Request__c> CDRList{get;set;}
    @auraEnabled 
    public List<Document_Change_Request__c> CDRUnapprovedList{get;set;} //used for approvers
    @auraEnabled 
    public List<Document_Change_Request__c> dcrRDocumentCordinatorList{get;set;}
    @auraEnabled 
    public List<Document_Change_Request__c> dcrReviewerList{get;set;}
    @auraEnabled 
    public List<Document_Change_Request__c> CDRAuthList{get;set;}  //Used to Authors
    @auraEnabled 
    public List<NC_Form__c> NCList{get;set;}
    @auraEnabled 
    public List<NC_Form__c> NcCreatedbyList{get;set;}
    @auraEnabled 
    public List<NC_Form__c> NcSendNotiList{get;set;}
    @auraEnabled 
    public List<NC_Form__c> NcSendNotiDataDetermineDisposition{get;set;}
    @auraEnabled 
    public List<CAR_Form__c> CARList{get;set;}
    @auraEnabled 
    public List<CAR_Form__c> CARListForAdmins{get;set;}
    @auraEnabled 
    public List<CAR_Form__c> CARListAnalyzeCause{get;set;}
    @auraEnabled 
    public List<CAR_Form__c> CARListEvaluateandClose{get;set;}
    @auraEnabled 
    public List<Work_Order__c> woList{get;set;}
    @auraEnabled 
    public List<Employee_Training__c> TrainingList{get;set;}
    @auraEnabled 
    public List<Employee_Training__c> TrainingList2{get;set;}
    @auraEnabled 
    public List<Audit_Calendar__c> auditAudList{get;set;}
    @auraEnabled 
    public List<Audit_Calendar__c> auditAuditeList{get;set;}
    @AuraEnabled
    public List<Audit_Calendar__c> auditModuleManagerList{get;set;}
    @auraEnabled 
    public List<Management_Review__c> ManagementList{get;set;}
    @auraEnabled 
    public List<Management_Review__c> ManagementUpdatedList{get;set;}
    @auraEnabled 
    public List<Action_Plan__c> actionPlnList{get;set;}
    @auraEnabled 
    public List<Training_Management__c> cordinatingTrainingList{get;set;}
    @auraEnabled 
    public List<Training_Management__c> pendingTrainingList{get;set;}
    @auraEnabled 
    public List<Training_Management__c> scheduledTrainingList{get;set;}
    @auraEnabled 
    public List<Training_Management__c> pendingVerifyTrainingList{get;set;}
}