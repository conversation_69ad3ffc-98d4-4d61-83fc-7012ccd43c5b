@isTest
public class TestCMT_Utils {

    @testSetup static void settingUpData() {
        profile pr = [SELECT id FROM profile WHERE name = 'System Administrator'];
        User newUser = new User(
            profileId = pr.id,
            username = '<EMAIL>',
            email = '<EMAIL>',
            emailencodingkey = 'UTF-8',
            localesidkey = 'en_US',
            languagelocalekey = 'en_US',
            timezonesidkey = 'America/Los_Angeles',
            alias = 'nuser',
            lastname = 'lastname'
        );
        insert newUser;

        TestDataFactory.createAuditConfigurations(1);
        Audit_Configuration_List__c audit = [SELECT Id FROM Audit_Configuration_List__c LIMIT 1];
        audit.Email_Notifications__c = true;
        update audit;
    }


    @isTest
    static void testGetAuditConfiguration() {
       
        Audit_Configuration_List__c audit = CMT_Utils.getAuditConfiguration();
        System.assertNotEquals(null, audit.Id);
    
    }

    @isTest
    static void testSendCustomEmail() {
        // Prepare Test Data
        User newUser = [SELECT Id, Name FROM User WHERE Username = '<EMAIL>'];

        Set<String> setOfIds = new Set<String>();
        setOfIds.add(newUser.Id);
        Test.startTest();
        CMT_Utils.sendCustomEmail(setOfIds, 'Test Subject', 'Test Body', true);
        Test.stopTest();

        // Asserts can be improved based on the function's result.
        // For example, you can use Limits class to check number of emails sent.
    }

    @isTest
    static void testCreateTasks() {
        User newUser = [SELECT Id, Name FROM User WHERE Username = '<EMAIL>'];

        Set<String> setOfIds = new Set<String>();
        setOfIds.add(newUser.Id);
        
        // RecordId based on your org configuration
        Id recordId; 

        Test.startTest();
        CMT_Utils.createTasks('Test Task', 'Test Task Body', setOfIds, recordId);
        Test.stopTest();

        List<Task> tasks = [SELECT Id, OwnerId, Subject, Description, Status FROM Task WHERE OwnerId = :newUser.Id];
        System.assertEquals(1, tasks.size());
    }
    
    @isTest
    static void testGetInProgressTaskCountByRecord() {
        // Prepare Test Data
        User newUser = [SELECT Id, Name FROM User WHERE Username = '<EMAIL>'];


        Task t = new Task(OwnerId = newUser.Id, Status = 'Open');
        // Fill in the necessary fields based on your org configuration
        insert t;

        Set<Id> recordIds = new Set<Id>();
        recordIds.add(t.WhatId);

        Map<Id, Integer> recordTaskCount = CMT_Utils.getInProgressTaskCountByRecord(recordIds, newUser.Id);
        System.assertEquals(1, recordTaskCount.size());
        System.assertEquals(1, recordTaskCount.get(t.WhatId));
    }

    @isTest
    private static void testCreateNote() {
        User newUser = [SELECT Id, Name FROM User WHERE Username = '<EMAIL>'];

        Set<String> setOfIds = new Set<String>();
        setOfIds.add(newUser.Id);

        // RecordId based on your org configuration
        Account acc = new Account(Name = 'Main Account');
        insert acc;
        Document_Change_Request__c dcr = new Document_Change_Request__c(
            Account__c = acc.Id
        );
        insert dcr;
        Id recordId = dcr.Id;

        Test.startTest();
        CMT_Utils.createNote(recordId, 'Test Note Body with length more than 20');
        Test.stopTest();
    }

    @isTest
    private static void testCreateNoteAndNotify() {
        User newUser = [SELECT Id, Name FROM User WHERE Username = '<EMAIL>'];

        Set<String> setOfIds = new Set<String>();
        setOfIds.add(newUser.Id);

        // RecordId based on your org configuration
        Account acc = new Account(Name = 'Main Account');
        insert acc;
        Document_Change_Request__c dcr = new Document_Change_Request__c(
            Account__c = acc.Id
        );
        insert dcr;
        Id recordId = dcr.Id;

        Test.startTest();
        CMT_Utils.createNoteAndNotify(recordId, 'Test Note Body with length more than 20');
        Test.stopTest();
    }

    // Continue adding tests for other methods in the same way
    @IsTest
    private static void testNavigationMenuItemsController() {
        System.Test.startTest();
        NavigationMenuItemsController controller = new NavigationMenuItemsController();
        List<NavigationMenuItem> items = NavigationMenuItemsController.getNavigationMenuItems('Default Navigation', 'Published');
        System.Test.stopTest();

        Assert.isTrue(items != null);
    }
}
