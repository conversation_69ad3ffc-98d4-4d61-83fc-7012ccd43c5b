@isTest
public class change_doc_statusTest {

    @TestSetup
    private static void testSetup() {
        TestDataFactory.createAccounts(1);

        Account acc = [SELECT Id FROM Account LIMIT 1];

        List<Contact> Lstcon = TestDataFactory.craeteContact(1, acc.Id);
        Contact con = [SELECT AccountId FROM Contact WHERE id = :Lstcon[0].Id];

        Profile p = [SELECT Id FROM Profile WHERE Name IN ('Customer Community Plus User') LIMIT 1];
        User newUser = new User(
            ProfileId = p.Id,
            Username = '<EMAIL>',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LocaleSidKey = 'en_US',
            LanguageLocaleKey = 'en_US',
            TimeZoneSidKey = 'America/Los_Angeles',
            Alias = 'nuser',
            LastName = 'lastname',
            ContactId = con.Id
        );
        insert newUser;

        TestDataFactory.createAuditConfigurations(1);
        Audit_Configuration_List__c aucoListRec =  [SELECT Id FROM Audit_Configuration_List__c LIMIT 1];
        aucoListRec.AccountId__c = con.AccountId;
        aucoListRec.Client_Admin__c = newUser.Id;
        aucoListRec.TrainingProgram__c = newUser.Id;
        update aucoListRec;

        System.runAs(newUser) {
            List<Document__c> documents = new List<Document__c>{
                new Document__c (
                    Document_Title__c = 'Title 1',
                    Account__c = acc.Id
                ),
                new Document__c (
                    Document_Title__c = 'Title 2',
                    Account__c = acc.Id
                )
            };
            insert documents;

            List<Document_Change_Request__c> changeRequests = new List<Document_Change_Request__c> {
                new Document_Change_Request__c(
                    Document_Title__c = 'Title 11',
                    Account__c = acc.Id,
                    Document__c = documents.get(0).Id,
                    Status__c = 'Active'
                ),
                new Document_Change_Request__c(
                    Document_Title__c = 'Title 21',
                    Account__c = acc.Id,
                    Document__c = documents.get(1).Id,
                    Status__c = 'Active'
                )
            };
            insert changeRequests;
        }
    }
    
    @isTest
    public static void beforeTest() {
        Document_Change_Request__c dcr1 = [SELECT Id FROM Document_Change_Request__c WHERE Document_Title__c = 'Title 11' LIMIT 1];
        User communityUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];

        System.Test.startTest();

        System.runAs(communityUser) {

            dcr1.Status__c = 'Pending Final Approval';
            update dcr1;

            insert new Task(WhatId = dcr1.Id, Subject = 'Test task');

            dcr1.Status__c = 'In Review';
            update dcr1;
        }

        System.Test.stopTest();
    }

    @IsTest
    private static void testInsertValidationUniqueTitle() {
        Account acc = [SELECT Id FROM Account LIMIT 1];
        User communityUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        Document__c document1 = [SELECT Id FROM Document__c WHERE Document_Title__c = 'Title 1' LIMIT 1];

        List<Document__c> documents = new List<Document__c> {
            new Document__c(Document_Title__c = 'Title 1', Account__c = acc.Id),
            new Document__c(Document_Title__c = 'Title 3', Account__c = acc.Id)
        };

        System.Test.startTest();
        List<Database.SaveResult> saveResults = Database.insert(documents, false);
        List<Database.SaveResult> saveResultsCR = new List<Database.SaveResult>();

        System.runAs(communityUser) {

            List<Document_Change_Request__c> changeRequests = new List<Document_Change_Request__c>{
                new Document_Change_Request__c(
                    Document_Title__c = 'Title 11',
                    Account__c = acc.Id,
                    Document__c = document1.Id,
                    Status__c = 'Active'
                ),
                new Document_Change_Request__c(
                    Document_Title__c = 'Title 21',
                    Account__c = acc.Id,
                    Document__c = document1.Id,
                    Status__c = 'Active'
                )
            };
            saveResultsCR = Database.insert(changeRequests, false);
        }
        System.Test.stopTest();

        Assert.areEqual(2, saveResults.size(), 'There are 2 Documents on insert.');
        Assert.isFalse(saveResults.get(0).isSuccess(), 'First Document has duplicate value "Title 1".');
        Assert.areEqual('The document title already exists in another record!', saveResults.get(0).getErrors().get(0).getMessage());
        Assert.isTrue(saveResults.get(1).isSuccess(), JSON.serialize(saveResults.get(1).getErrors()));

        Assert.isTrue(saveResultsCR.get(0).isSuccess(), 'Change Request can have title as related change request in the same Document.');
        Assert.isFalse(saveResultsCR.get(1).isSuccess(), 'Change Request Title similar to change request for another document.');
        Assert.areEqual('The document title already exists in another record!', saveResultsCR.get(1).getErrors().get(0).getMessage());
    }

    @IsTest
    private static void testUpdateValidationUniqueTitle() {
        Document__c document1 = [SELECT Id FROM Document__c WHERE Document_Title__c = 'Title 1' LIMIT 1];

        System.Test.startTest();
        document1.Document_Title__c = 'Title 2';
        Database.SaveResult saveResult = Database.update(document1, false);

        System.Test.stopTest();

        Assert.isFalse(saveResult.isSuccess(), 'Document has duplicate value "Title 1".');
        Assert.areEqual('The document title already exists in another record!', saveResult.getErrors().get(0).getMessage());
    }
}