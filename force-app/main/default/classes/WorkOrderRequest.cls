public with sharing class WorkOrderRequest {
    @AuraEnabled 
    public String requestedBy {get;set;}
    @AuraEnabled 
    public String dateOfRequest {get;set;} //Actual type Date
    @AuraEnabled 
    public String dateMaintenanceNeeded {get;set;} //Actual type Date
    @AuraEnabled 
    public String urgency {get;set;} 
    @AuraEnabled 
    public String safety {get;set;}
    @AuraEnabled 
    public String shutdown {get;set;}
    @AuraEnabled 
    public String timeOfMaintenance {get;set;}
    @AuraEnabled 
    public String locationOfAsset {get;set;}
    @AuraEnabled 
    public String assetId {get;set;}
    @AuraEnabled 
    public String issueNoticed {get;set;}
    @AuraEnabled 
    public String dateOfRequestManager {get;set;} //Actual type Date
    @AuraEnabled 
    public String descriptionOfMaintenance {get;set;}
    @AuraEnabled 
    public String dueDate {get;set;} //Actual Date
    @AuraEnabled 
    public String estimateTime {get;set;}
    @AuraEnabled 
    public String assignedTo {get;set;}
    @AuraEnabled 
    public String descriptionOfWork {get;set;}
    @AuraEnabled 
    public String lingeringIssue {get;set;}
    @AuraEnabled 
    public String descWorkNotDoable {get;set;}
    @AuraEnabled 
    public String maintenancePerformedDt {get;set;} //Actual DT
    @AuraEnabled 
    public String maintenanceTimeLength {get;set;}
    @AuraEnabled 
    public List<String> idealTimeOfMaintenance {get;set;}
    @AuraEnabled 
    public List<String> idealDaysOfMaintenance {get;set;}
    @AuraEnabled 
    public String type {get;set;}
    @AuraEnabled 
    public String WorkOrderType {get;set;}
    @AuraEnabled 
    public String technicianStatus {get;set;}
    @AuraEnabled 
    public String status {get;set;}
    @AuraEnabled 
    public String rejectReason {get;set;}
    public WorkOrderRequest(){
        
    }
}