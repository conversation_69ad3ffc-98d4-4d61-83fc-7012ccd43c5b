public with sharing class userWrapper {
    @AuraEnabled
    public String firstName{get;set;}
    @AuraEnabled
    public String lastName{get;set;} 
    @AuraEnabled
    public String employeeName{get;set;} 
    @AuraEnabled
    public String email{get;set;}
    @AuraEnabled
    public String phone{get;set;}
    @AuraEnabled
    public String code{get;set;}
    @AuraEnabled
    public String city{get;set;}
    @AuraEnabled
    public String state{get;set;}
    @AuraEnabled
    public String zipCode{get;set;}
    @AuraEnabled
    public String mailingAddress{get;set;}
    @AuraEnabled
    public String country{get;set;}
}