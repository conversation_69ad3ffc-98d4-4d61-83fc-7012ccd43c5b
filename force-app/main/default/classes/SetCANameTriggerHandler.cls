public class SetCANameTriggerHandler {
    
    public static void changeName(List<CAR_Form__c> newCAList, Boolean isInsert)
    {
        try {
            if(AccessibilityManager.isAccessible('CAR_Form__c')) {
                List<CAR_Form__c> correctiveAction = [SELECT Id, Name__c FROM CAR_Form__c  ORDER BY Name__c DESC LIMIT 1];
                //Populate Name__c like Auto-number, in case record is new and stage is not Draft if Draft - Draft Name__c created
                for (CAR_Form__c car: newCAList) {
                    String recordNumber = getLatestCorrectiveActionNumber(correctiveAction);
                    car.Name__c = 'CA-' + recordNumber.substring(recordNumber.length() - 5);
                }
            } else {
                throw new AuraHandledException('No access to object CAR_Form__c');
            }
        } catch(Exception e){
            HandleCustomException.LogException(e);
            throw new AurahandledException(e.getMessage());
        }
    }
    
    public static String getLatestCorrectiveActionNumber (List<CAR_Form__c> correctiveAction) {
        Integer latestNumber = 1;
        String recordNumber = '00000';

        if(correctiveAction.size() > 0) {
            latestNumber += Integer.valueOf(correctiveAction[0].Name__c.substringAfter('-').trim());
            recordNumber = '00000' + String.valueOf(latestNumber);
            return recordNumber;
        }
        return '00001';
    }

    public static void sendCANotificationForAssignOwner(CAR_Form__c carObj){
        List<String> receiverIds = new List<String>();            
        Audit_Configuration_List__c config = CMT_Utils.getAuditConfiguration();
        if(config != null && String.isNotBlank(config.NCCAIncharge__c)){ 
            receiverIds = config.NCCAIncharge__c.split(';');
        }
        String emailBody = '<html><p>New Corrective action record has been created. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__caId='+ carObj.Id +'">' + carObj.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);
    }


    public static void sendCANotificationForResolved(CAR_Form__c carObj){
        List<String> receiverIds = new List<String>();            
        receiverIds.add(carObj.CreatedById);
        receiverIds.add(carObj.cocmd__Manager_who_assigned_owner__c);
        receiverIds.add(carObj.Root_Cause_Analyzed_by__c);
        receiverIds.add(carObj.cocmd__Implementor__c);
        String emailBody = '<html><p>Corrective action record has been resolved. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__caId='+ carObj.Id +'">' + carObj.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);
    }

    public static void sendCANotificationForAnalyzeCause(CAR_Form__c carObj){
        List<String> receiverIds = new List<String>();            
        receiverIds.add(carObj.cocmd__CAR_User_Assigned__c);
        String emailBody = '<html><p>Corrective action record has been assigned to you to Analyze Cause. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__caId='+ carObj.Id +'">' + carObj.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);
    }

    public static void sendCANotificationForImplement(CAR_Form__c carObj){
        List<String> receiverIds = new List<String>();            
        receiverIds.add(carObj.cocmd__Implementor__c);
        String emailBody = '<html><p>Corrective action record has been assigned to you to implement. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__caId='+ carObj.Id +'">' + carObj.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);
    }

    public static void sendCANotificationForEvaluateandClose(CAR_Form__c carObj){
        List<String> receiverIds = new List<String>();            
        Audit_Configuration_List__c config = CMT_Utils.getAuditConfiguration();
        if(config != null && String.isNotBlank(config.NCCAIncharge__c)){ 
            receiverIds = config.NCCAIncharge__c.split(';');
        }
        String emailBody = '<html><p>Corrective action record is ready to Evaluate and Close. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__caId='+ carObj.Id +'">' + carObj.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);
    }

}