public with sharing class TestDataFactory {
    
    public static List<Account> createAccounts(Integer numAccts){	
        List<Account> AccLst = new List<Account>();
        for(Integer i=0;i<numAccts;i++) {
            Account  objAcc = new Account();
            objAcc.Name='Test Account';
            AccLst.add(objAcc);
        }
        list<string> fields = new list<string>{'Name'};
            if(AccessibilityManager.isCreateable(fields,'Account') && AccessibilityManager.isUpdateable('Contact') && AccessibilityManager.isUpdateable('User')){
                insert AccLst;
            }
        
        return AccLst;
    }
    public static List<contact> craeteContact(Integer numcont,Id Accountid){	
        List<Contact> conLst = new List<Contact>();
        for(Integer i=0;i<numcont;i++) {
            Contact con = new Contact (
                AccountId = Accountid,
                LastName = 'portalTestUser'
            );
            conLst.add(con);
        }
        if(AccessibilityManager.isCreateable('Contact')){
            insert conLst;
        }  
        return conLst;
    }
    public static List<Employee__c> createEmployees(Integer numcont){
        List<Account> accList = createAccounts(1);
        createDepartments(1);
        List<Department__c> depart = new List<Department__c>();
        if(AccessibilityManager.isAccessible('Department__c')) {
            depart = [Select id,name from department__c];
        }
        List<Employee__c> empLst = new List<Employee__c>();
        for(Integer i=0;i<numcont;i++) {
            Employee__c emp = new Employee__c (
                Account__c = accList[0].ID,
                Name = 'Test',
                Department_str__c = 'Test',
                Department__c = depart[0].Id,
                Supervisor__c = UserInfo.getUserId(),
                IsActive__c = true
            );
            empLst.add(emp);
        } 
        if(AccessibilityManager.isCreateable('Employee__c')){
            insert empLst;
        }
        return empLst;
    }

    public static List<Employee__c> createEmployees(Integer numcont, Id accountID){
        createDepartments(1);
        List<Department__c> depart = new List<Department__c>();
        if(AccessibilityManager.isAccessible('Department__c')) {
            depart = [Select id,name from department__c];
        }
        List<Employee__c> empLst = new List<Employee__c>();
        for(Integer i=0;i<numcont;i++) {
            Employee__c emp = new Employee__c (
                Account__c = accountID,
                Name = 'Test',
                Department_str__c = 'Test',
                Department__c = depart[0].Id,
                Supervisor__c = UserInfo.getUserId(),
                IsActive__c = true
            );
            empLst.add(emp);
        } 
        if(AccessibilityManager.isCreateable('Employee__c')){
            insert empLst;
        }
        return empLst;
    }

    public static List<Employee__c> createEmployees(Integer numcont, Id accountID, Id userId){
        createDepartments(1);
        List<Department__c> depart = new List<Department__c>();
        if(AccessibilityManager.isAccessible('Department__c')) {
            depart = [Select id,name from department__c];
        }
        List<Employee__c> empLst = new List<Employee__c>();
        for(Integer i=0;i<numcont;i++) {
            Employee__c emp = new Employee__c (
                Account__c = accountID,
                Name = 'Test',
                Department_str__c = 'Test',
                Department__c = depart[0].Id,
                User__c = userId,
                Supervisor__c = UserInfo.getUserId(),
                IsActive__c = true
            );
            empLst.add(emp);
        } 
        if(AccessibilityManager.isCreateable('Employee__c')){
            insert empLst;
        }
        return empLst;
    }

    public static List<Training_Management__c> createTrainingManagements(Integer numcont){
        List<Account> accList = createAccounts(1);
        List<Training_Management__c> trLst = new List<Training_Management__c>();
        for(Integer i=0;i<numcont;i++) {
            Training_Management__c tr = new Training_Management__c (
                Account__c = accList[0].ID
            );
            trLst.add(tr);
        }
        if(AccessibilityManager.isCreateable('Account')){
            insert trLst;
        }
        return trLst;
    }

    public static List<Training_Management__c> createTrainingManagements(Integer numcont, Id acntId){
        // List<Account> accList = createAccounts(1);
        List<Training_Management__c> trLst = new List<Training_Management__c>();
        for(Integer i=0;i<numcont;i++) {
            Training_Management__c tr = new Training_Management__c (
                Account__c = acntId
            );
            trLst.add(tr);
        }
        if(AccessibilityManager.isCreateable('Account')){
            insert trLst;
        }
        return trLst;
    }
    public static List<Employee_Training__c> createEmployeeTraining(Integer numcont){
        List<Account> accList = createAccounts(1);
        createTrainingManagements(1);
        Training_Management__c trm = new Training_Management__c();
        if(AccessibilityManager.isAccessible('Training_Management__c')) {
            trm = [Select id,name from Training_Management__c limit 1];
        }
        List<Employee_Training__c> trLst = new List<Employee_Training__c>();
        for(Integer i=0;i<numcont;i++) {
            Employee_Training__c tr = new Employee_Training__c (
                Account__c = accList[0].ID,
                Status__c = 'Approved',
                Training_Management__c = trm.ID                
            );
            trLst.add(tr);
            Employee_Training__c tr1 = new Employee_Training__c (
                Account__c = accList[0].ID,
                Status__c = 'Pending Approval',
                Training_Management__c = trm.ID
            );
            trLst.add(tr1);
             Employee_Training__c tr2 = new Employee_Training__c (
                Account__c = accList[0].ID,
                Status__c = 'Completed',
                Training_Management__c = trm.ID
            );
            trLst.add(tr2);
        } 
        if(AccessibilityManager.isCreateable('Training_Management__c')){
            insert trLst;
        }
        return trLst;
    }
    
    public static List<Employee_Training__c> createEmployeeTraining(Integer numcont, Id accntId, Id trainingManagementId){
        // List<Account> accList = createAccounts(1);
        // createTrainingManagements(1);
        
        List<Employee_Training__c> trLst = new List<Employee_Training__c>();
        for(Integer i=0;i<numcont;i++) {
            Employee_Training__c tr = new Employee_Training__c (
                Account__c = accntId,
                Status__c = 'Approved',
                Training_Management__c = trainingManagementId
            );
            trLst.add(tr);
        } 
        if(AccessibilityManager.isCreateable('Training_Management__c')){
            insert trLst;
        }
        return trLst;
    }

    public static void createAuditCalender(Integer numcont){
        List<Account> accList = createAccounts(1);
        List<Audit_Calendar__c> adtLst = new List<Audit_Calendar__c>();
        for(Integer i=0;i<numcont;i++) {
            Audit_Calendar__c adt = new Audit_Calendar__c (
                Account__c = accList[0].ID, 
                Audit_Status__c = 'Scheduled',
                Audit_Auditor__c = 'Test',
                Schedule_Data__c = dateTime.newInstance(date.newInstance(1998, 12, 1), time.newInstance(2, 2, 2, 2))
            );
            adtLst.add(adt);
        } 
        if(AccessibilityManager.isCreateable('Audit_Calendar__c')){
            insert adtLst; 
        }  
    }
    public static void createTrainingCourse(Integer numcont){
        List<Account> accList = createAccounts(1);
        List<Training_Course__c> trLst = new List<Training_Course__c>();
        for(Integer i=0;i<numcont;i++) {
            Training_Course__c tr = new Training_Course__c (
                Name = 'Test',
                Trainer__c = 'test'
            );
            trLst.add(tr);
        }
        if(AccessibilityManager.isCreateable('Training_Course__c')){
            insert trLst; 
        }  
    }

    public static List<Management_Review__c> createManagementReviews(Integer numcont){
        List<Account> accList = createAccounts(1);
        List<Management_Review__c> mrLst = new List<Management_Review__c>();
        for(Integer i=0;i<numcont;i++) {
            Management_Review__c mr = new Management_Review__c (
                Account__c = accList[0].ID,
                Meeting_Status_Type__c = 'Scheduled'
            );
            mrLst.add(mr);
        } 
        if(AccessibilityManager.isCreateable('Management_Review__c')){
            insert mrLst;
        } 
        return mrLst;
    } 
    public static List<Management_Review__c> createManagementReviews(Integer numcont, Id accountId){
        
        List<Management_Review__c> mrLst = new List<Management_Review__c>();
        for(Integer i=0;i<numcont;i++) {
            Management_Review__c mr = new Management_Review__c (
                Account__c = accountId,
                Meeting_Status_Type__c = 'Scheduled'
            );
            mrLst.add(mr);
        } 
        if(AccessibilityManager.isCreateable('Management_Review__c')){
            insert mrLst;
        } 
        return mrLst;
    } 

    public static void createParticipents(Integer numcont){
        List<Management_Review__c> mgtList = createManagementReviews(1);
        List<Account> accList = createAccounts(1);
        List<Participants__c> ptLst = new List<Participants__c>();
        for(Integer i=0;i<numcont;i++) {
            Participants__c pt = new Participants__c (
                Meeting_Id__c = mgtList[0].ID,
                Participant_Name__c = UserInfo.getUserId(),
                Account__c  = accList[0].ID
            );
            ptLst.add(pt);
        } 
        if(AccessibilityManager.isCreateable('Participants__c')){
            insert ptLst;   
        }
    }   
    Public static List<user> createUsers(Integer numusers,Id ProfileId){
        List<user> uLst = new List<user>();
        
        for(Integer i=0;i<numusers;i++) {
            User newUser = new User(
                profileId = ProfileId,
                username = 'newUser'+i+'@yahoo.com',
                email = '<EMAIL>',
                emailencodingkey = 'UTF-8',
                localesidkey = 'en_US',
                languagelocalekey = 'en_US',
                timezonesidkey = 'America/Los_Angeles',
                alias='nuser',
                lastname='lastname'
            );
            uLst.add(newUser);
        }
        if(AccessibilityManager.isCreateable('user'))
        {
            insert uLst;
        }
        
        return uLst;
    }

    public static List<user> createUsers(Integer numusers,Id ProfileId, Id contactId){
        List<user> uLst = new List<user>();
        
        for(Integer i=0;i<numusers;i++) {
            User newUser = new User(
                profileId = ProfileId,
                username = 'newUser'+i+'@yahoo.com',
                email = '<EMAIL>',
                emailencodingkey = 'UTF-8',
                localesidkey = 'en_US',
                languagelocalekey = 'en_US',
                timezonesidkey = 'America/Los_Angeles',
                alias='nuser',
                lastname='lastname',
                contactId = contactId
            );
            uLst.add(newUser);
        }
        if(AccessibilityManager.isCreateable('user'))
        {
            insert uLst;
        }
        
        return uLst;
    }

    public static List<Training_Skill__c> createTrainingSkills(Integer numcont){
        List<Training_Skill__c> trLst = new List<Training_Skill__c>();
        for(Integer i=0;i<numcont;i++) {
            Training_Skill__c tr = new Training_Skill__c (
                Name = 'Test'
            );
            trLst.add(tr);
        } 
        if(AccessibilityManager.isCreateable('Training_Skill__c')){
            insert trLst;  
        }
        return trLst;
    }
    public static List<Department__c> createDepartments(Integer numcont){
        createAccounts(1);
        List<Account> accList = new List<Account>();
        if(AccessibilityManager.isAccessible('account')) {
            accList = [Select id,name from account];
        }
        List<Department__c> trLst = new List<Department__c>();
        for(Integer i=0;i<numcont;i++) {
            Department__c tr = new Department__c (
                Name = 'Test',
                Account__c = accList[0].ID
            );
            trLst.add(tr);
        } 
        if(AccessibilityManager.isCreateable('Department__c')){
            insert trLst;  
        }
        return trLst;
    }
    public static void createTrainingTopics(Integer numcont){
        createAccounts(1);
        createDepartments(1);
        createTrainingSkills(1);
        createEmployees(1);
        List<Account> accList = new List<Account>();
        if(AccessibilityManager.isAccessible('Account')) {
            accList = [Select id,name from account];
        }
        List<Training_Skill__c> trSkillList = new List<Training_Skill__c>();
        if(AccessibilityManager.isAccessible('Training_Skill__c')) {
            trSkillList = [Select id,name from Training_Skill__c];
        }
        List<Department__c> dptList = new List<Department__c>();
        if(AccessibilityManager.isAccessible('Department__c')) {
            dptList = [Select id,name from Department__c];
        }
        List<Training_Topic__c> trLst = new List<Training_Topic__c>();
        for(Integer i=0;i<numcont;i++) {
            Training_Topic__c tr = new Training_Topic__c (
                Department__c= dptList[0].Id,
                Training_Skill__c= trSkillList[0].Id,
                External_Id__c = '123456'
            );
            trLst.add(tr);
        } 
        if(AccessibilityManager.isCreateable('Training_Topic__c')){
            insert trLst; 
        } 
    }

    public static void createTrainingTopics(Integer numcont, Id departId, Id trainingSkillId, Id empId){
        List<Training_Topic__c> trLst = new List<Training_Topic__c>();
        for(Integer i=0;i<numcont;i++) {
            Training_Topic__c tr = new Training_Topic__c (
                Department__c= departId,
                Training_Skill__c= trainingSkillId,
                External_Id__c = empId
            );
            trLst.add(tr);
        } 
        if(AccessibilityManager.isCreateable('Training_Topic__c')){
            insert trLst; 
        } 
    }

    public static void createWorkOrders(Integer numcont){
        createAccounts(1);
        createDepartments(1);
        createTrainingSkills(1);
        createEmployees(1);
        List<Account> accList = new List<Account>();
        if(AccessibilityManager.isAccessible('Account')) {
            accList = [Select id,name from account];
        }
        List<Training_Skill__c> trSkillList = new List<Training_Skill__c>();
        if(AccessibilityManager.isAccessible('Training_Skill__c')) {
            trSkillList = [Select id,name from Training_Skill__c];
        }
        List<Department__c> dptList = new List<Department__c>();
        if(AccessibilityManager.isAccessible('Department__c')) {
            dptList = [Select id,name from Department__c];
        }
        List<Work_Order__c> trLst = new List<Work_Order__c>();
        for(Integer i=0;i<numcont;i++) {
            Work_Order__c tr = new Work_Order__c (
                account__c = accList[0].ID
            );
            trLst.add(tr);
        } 
        if(AccessibilityManager.isCreateable('Work_Order__c')){
            insert trLst; 
        } 
    }
    public static void createAssets(Integer numcont){
        createAccounts(1);
        createDepartments(1);
        createTrainingSkills(1);
        createEmployees(1);
        List<Account> accList = new List<Account>();
        if(AccessibilityManager.isAccessible('Account')) {
            accList = [Select id,name from account limit 1];
        }
        List<Training_Skill__c> trSkillList = new List<Training_Skill__c>();
        if(AccessibilityManager.isAccessible('Training_Skill__c')) {
            trSkillList = [Select id,name from Training_Skill__c];
        }
        List<Department__c> dptList = new List<Department__c>();
        if(AccessibilityManager.isAccessible('Department__c')) {
            dptList = [Select id,name from Department__c];
        }
        List<Asset__c> trLst = new List<Asset__c>();
        for(Integer i=0;i<numcont;i++) {
            Asset__c tr = new Asset__c (
                account__c = accList[0].ID,
                Department__c = dptList[0].ID
            );
            trLst.add(tr);
        } 
        if(AccessibilityManager.isCreateable('Asset__c')){
            insert trLst;  
        }
    }
    public static void createAssetScheduleMaintainance(Integer numcont){
        createAccounts(1);
        Account a = new Account();
        if(AccessibilityManager.isAccessible('Account')) { 
            a = [Select id from account LIMIT 1];
        }

        List<Asset_Scheduled_Maintenance__c> trLst = new List<Asset_Scheduled_Maintenance__c>();
        for(Integer i=0;i<numcont;i++) {
            Asset_Scheduled_Maintenance__c tr = new Asset_Scheduled_Maintenance__c ( account__c  = a.id);
            trLst.add(tr);
        } 
        if(AccessibilityManager.isCreateable('Asset_Scheduled_Maintenance__c')){
            insert trLst;
        }
        
    }
    @future
    public static void createCommunityUsers(String accID,String conId){
        
        
        UserRole userrole = [Select Id, DeveloperName From UserRole Limit 1];
        
        User adminUser = [Select Id, UserRoleId From User Where Id = :UserInfo.getUserId() Limit 1];
        
        
        if(AccessibilityManager.isUpdateable('User') && AccessibilityManager.isUpdateable('UserRole') && AccessibilityManager.isUpdateable('Contact') && AccessibilityManager.isUpdateable('Account')){
            adminUser.UserRoleId = userRole.Id;
            update adminUser;
        }
        
        Profile pfile = [Select id from profile where name = 'Customer Community User'];
        List<user> uLst = new List<user>();
        User newUser = new User(alias = 'test123', email='<EMAIL>',
                                emailencodingkey='UTF-8', lastname='Testing', languagelocalekey='en_US',
                                localesidkey='en_US', profileid = pfile.Id, country='United States',IsActive =true,
                                ContactId = conId,
                                timezonesidkey='America/Los_Angeles', username='<EMAIL>');
        uLst.add(newUser);   
        System.runAs(new User(Id = UserInfo.getUserId())) {
            if(AccessibilityManager.isCreateable('User') && AccessibilityManager.isUpdateable('Contact')){
                Insert uLst;
            }
        }
    }
    public static List<NC_Form__c> createNCForms(Integer numNC,Id AccountId){
        
        List<NC_Form__c> NCFrmLst = new List<NC_Form__c>();
        for(Integer i=0;i<numNC;i++) {
            NC_Form__c NC = new NC_Form__c (
                Account__c = AccountId,
                Stage__c = 'Identify and Contain',
                Name__c = 'Test'
            );
            NCFrmLst.add(NC);
        } 
        if(AccessibilityManager.isCreateable('NC_Form__c')){
            Insert NCFrmLst;
        }
       /* if(AccessibilityManager.isAccessible('NC_Form__c')){
            Insert NCFrmLst;
        }*/
        return NCFrmLst;
    }
    public static List<CAR_Form__c> createCarForms(Integer numCr){
         Account a = new Account();
        if(AccessibilityManager.isAccessible('Account')) { 
            a = [Select id from account LIMIT 1];
        }
        List<CAR_Form__c> crFrmLst = new List<CAR_Form__c>();
        for(Integer i=0;i<numCr;i++) {
            CAR_Form__c NC = new CAR_Form__c ( 
             Account__c = a.Id,
                Name__c = 'Test'
            );
            crFrmLst.add(NC);
        }   
        if(AccessibilityManager.isCreateable('CAR_Form__c')){
            Insert crFrmLst;
        }
       /* if(AccessibilityManager.isAccessible('CAR_Form__c')){
            Insert crFrmLst;
        }*/
        return crFrmLst;
    }
   
    public static List<Audit_Checklist__c> createAuditchkLst(Integer numAuditchk){
        
        List<Audit_Checklist__c> AuditchkLst = new List<Audit_Checklist__c>();
        for(Integer i=0;i<numAuditchk;i++) {
            Audit_Checklist__c objChecklist= new Audit_Checklist__c();
            objChecklist.Name = '0.1';
            objChecklist.Question__c='How are u?';
            AuditchkLst.add(objChecklist);
        } 
        if(AccessibilityManager.isCreateable('Audit_Checklist__c')){
            insert AuditchkLst;
        }
        return AuditchkLst;
    }
    public static void createAuditConfigurations(Integer numAuditchk){
        List<Audit_Configuration_List__c> AuditchkLst = new List<Audit_Configuration_List__c>();
        // Profile pfile = [Select id from profile where name = 'Customer Community User'];
        for(Integer i=0;i<numAuditchk;i++) {
            Audit_Configuration_List__c objChecklist= new Audit_Configuration_List__c();
            objChecklist.Name = '0.1';
            objChecklist.TrainingProgram__c = 'Test';
            
            AuditchkLst.add(objChecklist);
        }
        if(AccessibilityManager.isCreateable('Audit_Configuration_List__c')){
            Insert AuditchkLst;
        }
    }
    public static void createAuditProcess(Integer numAuditchk){
        List<Audit_Process__c> AuditchkLst = new List<Audit_Process__c>();
        createAuditchkLst(1);
        createAccounts(1);
        Account acc = new Account();
         if(AccessibilityManager.isAccessible('Account')) { 
            acc = [Select id from account LIMIT 1];
        }

        Audit_Checklist__c ac = new Audit_Checklist__c();
        if(AccessibilityManager.isAccessible('Audit_Checklist__c')) { 
            ac = [Select id from Audit_Checklist__c LIMIT 1];
        }

        for(Integer i=0;i<numAuditchk;i++) {
            Audit_Process__c objChecklist= new Audit_Process__c();
            objChecklist.Name = 'Test';
            objChecklist.Account__c = acc.Id;
            AuditchkLst.add(objChecklist);
        } 
        if(AccessibilityManager.isCreateable('Audit_Process__c')){
            Insert AuditchkLst;
        }
    }
    public static void createAuditProcessClause(Integer numAuditchk){
        createAuditProcess(1);
        createAuditchkLst(1);
        Audit_Process__c ap = new Audit_Process__c();
        if(AccessibilityManager.isAccessible('Audit_Process__c')) {
            ap = [Select id from Audit_Process__c LIMIT 1];
        }
        List<Audit_Process_Clause__c> AuditchkLst = new List<Audit_Process_Clause__c>();
        Audit_Checklist__c ac = new Audit_Checklist__c();
        if(AccessibilityManager.isAccessible('Audit_Checklist__c')) {
            ac = [Select id from Audit_Checklist__c LIMIT 1];
        }
        for(Integer i=0;i<numAuditchk;i++) {
            Audit_Process_Clause__c objChecklist= new Audit_Process_Clause__c();
            objChecklist.Name = 'Test';
            objChecklist.Audit_Checklist__c = ac.id;
            objChecklist.Audit_Process__c = ap.Id;
            AuditchkLst.add(objChecklist);
        }    
        if(AccessibilityManager.isCreateable('Audit_Process_Clause__c')){
            Insert AuditchkLst;
        }
    }
    public static List<Auditor_Response__c> createAuditResLst(Integer numAuditRes,Id AuditchklstId){
        
        List<Auditor_Response__c> AuditResLst = new List<Auditor_Response__c>();
        for(Integer i=0;i<numAuditRes;i++) {
            Auditor_Response__c objChecklist= new Auditor_Response__c();
            objChecklist.Audit_Checklist__c = AuditchklstId;
            AuditResLst.add(objChecklist);
        }    
        return AuditResLst;
    }  
    public static List<Audit_Configuration_List__c> createAuditConfigLst(Integer numAuditConfigLst ,String AccountId,String AuditIncharge,String client_admin,
                                                                         String default_approver_id, String default_compliance,
                                                                         String default_manager_id,String default_nc_email_from,
                                                                         String default_party_id,String default_technitian_id,String trainingapprover,String training_effectiveness,
                                                                         String trainingprogram,String default_author_id)
    {	
        List<Audit_Configuration_List__c> AuditConfigLst = new List<Audit_Configuration_List__c>();
        
        for(Integer i=0;i<numAuditConfigLst;i++) {
            Audit_Configuration_List__c  ACL = new Audit_Configuration_List__c();
            ACL.Name='abc';
            ACL.accountid__c = AccountId;
            ACL.audit_incharge__c = AuditIncharge;
            ACL.client_admin__c = client_admin;
            ACL.create_work_order_days_before__c = 12;
            ACL.default_approver_id__c = default_approver_id;
            ACL.default_compliance__c = default_compliance;
            ACL.default_manager_id__c = default_manager_id;
            ACL.default_nc_email_from__c = default_nc_email_from;
            ACL.default_party_id__c = default_party_id;
            ACL.default_technitian_id__c = default_technitian_id;
            ACL.trainingapprover__c = trainingapprover;
            ACL.training_effectiveness__c = training_effectiveness;
            ACL.trainingprogram__c = trainingprogram;
            ACL.default_author_id__c = default_author_id;
            AuditConfigLst.add(ACL);
        }    
        return AuditConfigLst;
        
    }
    public static List<Account> createAccountsSobject(Integer numAccts){	
        List<Account> AccLst = new List<Account>();
        for(Integer i=0;i<numAccts;i++) {
            Account  objAcc = new Account();
            objAcc.Name='Test Account';
            AccLst.add(objAcc);
        }    
        return AccLst;
    }  
    
    // public static List<object> getFiles() {
    //     ContentVersion contentVers = new ContentVersion(Title= 'a picture',PathOnClient   = 'Pic.jpg',VersionData    = Blob.valueOf('Test Content'),IsMajorVersion = true);
    
    //     insert contentVers;
    
    //     List<ContentDocument> documents = [SELECT Id, Title, LatestPublishedVersionId FROM ContentDocument LIMIT 1];
    
    //     Map<String,object> filesObj = new MAp<String,Object>();
    //     filesObj.put('documentId', documents[0].id);
    //     filesObj.put('contentVersionId', contentVersion.id);
    //     filesObj.put('stageName', 'Implement');
    //     filesObj.put('IsTrainingMaterial', 'TrainerMaterial');
    //     filesObj.put('VersionData',contentVersion.VersionData);
    //     List<object> filesList = new List<object>();
    //     filesList.add(JSON.serialize(filesObj));
    //     return filesList;
    // }
    
    public static MeetingData1 getMeetingData(){
        MeetingData1 md = new MeetingData1();
        md.startDateTime = String.valueOf(System.now());
        md.endDateTime = String.valueOf(System.now());
        return md; 
    }

    public static List<cocmd__Department__c> createDepartment(Integer numOfDepts){
        List<cocmd__Department__c> deptObjLst = createDepartmentStub(numOfDepts);
        if(AccessibilityManager.isCreateable('cocmd__Department__c')){
            insert deptObjLst;
        }
        return deptObjLst;
    }

    public static List<cocmd__Department__c> createDepartmentStub(Integer numOfDepts){
        
        List<cocmd__Department__c> deptObjLst = new List<cocmd__Department__c>();
        for(Integer i=0;i<numOfDepts;i++) {
            cocmd__Department__c deptObj= new cocmd__Department__c();
            deptObj.Name = 'Test ' + i;
            deptObjLst.add(deptObj);
        }  
        return deptObjLst;
       
    }
}