@isTest
public class FilePreviewControllerTest {
    
    @TestSetup
    public static void testSetup() {

        TestDataFactory.createAccounts(1);

        Account a = [Select id, name from Account LIMIT 1];

        List<Contact> Lstcon = TestDataFactory.craeteContact(1, a.Id);
        Contact con = [Select id, AccountId from Contact where id = :Lstcon[0].id];

        Profile p = [select Id, name from Profile where Name in ('Customer Community Plus User') limit 1];
        User newUser = new User(
            profileId = p.id,
            contactId = con.id,
            username = '<EMAIL>',
            email = '<EMAIL>',
            emailencodingkey = 'UTF-8',
            localesidkey = 'en_US',
            languagelocalekey = 'en_US',
            timezonesidkey = 'America/Los_Angeles',
            alias = 'nuser',
            lastname = 'lastname'
        );
        insert newUser;

        TestDataFactory.createAuditConfigurations(1);
        Audit_Configuration_List__c aucoListRec = [Select id, AccountId__c from Audit_Configuration_List__c limit 1];
        aucoListRec.AccountId__c = con.AccountId;
        aucoListRec.TrainingProgram__c = newUser.id;
        update aucoListRec;
        TestDataFactory.createTrainingSkills(1);
        TestDataFactory.createDepartments(1);

        TestDataFactory.createTrainingManagements(1);
        TestDataFactory.createEmployees(1);
        TestDataFactory.createEmployeeTraining(1);
        TestDataFactory.createTrainingCourse(1);

        ContentVersion contentVersion = new ContentVersion(
            Title = 'a picture',
            PathOnClient = 'Pic.jpg',
            VersionData = Blob.valueOf('Test Content'),
            IsMajorVersion = true
        );
        insert contentVersion;

        ContentDocument document = [
            SELECT Title, LatestPublishedVersionId
            FROM ContentDocument
            WHERE LatestPublishedVersionId = :contentVersion.Id
            LIMIT 1
        ];

        System.runAs(newUser) {
            Document__c doc = new Document__c(
                Account__c = con.AccountId
                
            );
            insert doc;

            Document_Change_Request__c dcr = new Document_Change_Request__c(
                Account__c = con.AccountId,
                Document__c = doc.id,
                Status__c = 'Active'
                
            );
            insert dcr;
        }

        Document_Change_Request__c dcr = [
            SELECT Id
            FROM Document_Change_Request__c
            WHERE Account__c = :con.AccountId
            LIMIT 1
        ];

        //create ContentDocumentLink  record
        ContentDocumentLink cdl = new ContentDocumentLink();
        cdl.LinkedEntityId = dcr.Id;
        cdl.ContentDocumentId = document.Id;
        cdl.ShareType = 'V';
        cdl.Visibility = 'AllUsers';
        insert cdl;
    }
    
    @isTest
    public static void getFileUrlTest() {
        User newUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        System.assertEquals(true, newUser != null);
        PermissionSet psObj = [SELECT ID, Name FROM PermissionSet WHERE Name = 'Customer_Community_User_Access_To_Custom_Objects_Plus'];
        PermissionSetAssignment psaObj = new PermissionSetAssignment(
            PermissionSetId = psObj.Id,
            AssigneeId = newUser.Id
        );
        insert psaObj;

        Document_Change_Request__c dcr = [Select id from Document_Change_Request__c limit 1];

        Document__c doc = [Select id from Document__c limit 1];
        DataWrapper dataWrap = new DataWrapper();
        dataWrap.docStatus = '';
        dataWrap.fileUrl = '';
       
        System.runAs(newUser) {
             FilePreviewController.getFileUrl(dcr.id);
        }
    }

    @isTest
    public static void getFileUrlTest2() {
        User newUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];
        System.assertEquals(true, newUser != null);
        PermissionSet psObj = [SELECT ID, Name FROM PermissionSet WHERE Name = 'Customer_Community_User_Access_To_Custom_Objects_Plus'];
        PermissionSetAssignment psaObj = new PermissionSetAssignment(
            PermissionSetId = psObj.Id,
            AssigneeId = newUser.Id
        );
        insert psaObj;

        Document_Change_Request__c dcr = [Select id from Document_Change_Request__c limit 1];
        Document__c doc = [Select id from Document__c limit 1];
        DataWrapper dataWrap = new DataWrapper();
        dataWrap.docStatus = '';
        dataWrap.fileUrl = '';
       
        System.runAs(newUser) {
            FilePreviewController.getFileUrl(doc.id);
        }
    }
    
    //**************************************************************
    @isTest
    public static void getFileUrlTest3() {
        Document_Change_Request__c dcr = [Select id from Document_Change_Request__c limit 1];
        Document__c doc = [Select id from Document__c limit 1];
        DataWrapper dataWrap = new DataWrapper();
        dataWrap.docStatus = '';
        dataWrap.fileUrl = '';
        
        
       ContentDistribution conDist = [Select Id from ContentDistribution];
        delete conDist;
        //System.runAs(newUser) {
            FilePreviewController.getFileUrl(doc.id);
        //}
    }
    
       
	
}