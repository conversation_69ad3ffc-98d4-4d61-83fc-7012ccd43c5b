@isTest
public class SetTrainingNameTriggerTest {
    
    
    @Future public static void testSetup(){
        
        TestDataFactory.createAccounts(1);
        
        Account a = [Select id,name from Account LIMIT 1];
        
        List<Contact> Lstcon = TestDataFactory.craeteContact(1,a.Id);
        Contact con = [Select id,AccountId from Contact where id=:Lstcon[0].id];
        
        Profile p = [select Id,name from Profile where Name in ('Customer Community User') limit 1];
        User newUser = new User(
            profileId = p.id,
            username = 'newUser'+'Test'+'@yahoo.com',
            email = '<EMAIL>',
            emailencodingkey = 'UTF-8',
            localesidkey = 'en_US',
            languagelocalekey = 'en_US',
            timezonesidkey = 'America/Los_Angeles',
            alias='nuser',
            lastname='lastname');
        newUser.contactID = con.id;
        Insert newUser;
        
        List<Training_Management__c> trLst = new List<Training_Management__c>();
        Training_Management__c tr = new Training_Management__c (
            Account__c =con.AccountId,Name__c='Test-Tst'
        );
        trLst.add(tr);
        insert trLst;
        
    }
    
    @isTest
    public static void beforeTest() {
        
        testSetup();
        
        Test.startTest();
        Test.stopTest();
        
        User newUser = [Select id,ProfileId,contact.AccountId from User ORDER BY CreatedDate DESC limit 1];
        System.assertEquals(true, newUser != null);
        PermissionSet psObj = [SELECT ID, Name FROM PermissionSet WHERE Name = 'Customer_Community_User_Access_To_Custom_Objects'];
        PermissionSetAssignment psaObj = new PermissionSetAssignment(
            PermissionSetId = psObj.Id,
            AssigneeId = newUser.Id
        );
        insert psaObj;
        System.runAs(newUser) {
            
            
            List<Training_Management__c> trLst = new List<Training_Management__c>();
            Training_Management__c tr = new Training_Management__c (
                Account__c =newUser.contact.AccountId,Name__c='Test-Tst'
            );
            trLst.add(tr);
            insert trLst;
        } 
        
    }
    
    
    
    
}