public with sharing class MeetingData1 {
    @auraenabled
    public String meetingId {get;set;} //Meeting Name
    @auraenabled
    public String subject {get;set;}
    @auraenabled
    public String frequency {get;set;}
    @auraenabled
    public String meetingType {get;set;}
    @auraenabled
    public String agenda {get;set;} //Agenda Id
    @auraenabled
    public String description {get;set;} //Agenda description
    @auraenabled
    public String startDateTime {get;set;}
    @auraenabled
    public String endDateTime {get;set;}
    @auraenabled
    public Double duration {get;set;}
    @auraenabled
    public String meetingStatusType {get;set;}
    @auraenabled
    public String location {get;set;}
    @auraenabled
    public String participantsLst {get;set;}
    @auraenabled
    public List<Participant> participantList  {get;set;}
    /* @auraenabled
    public String startTime {get;set;}
    @auraenabled
    public Date startDate {get;set;} */
    
    //For files
    @auraenabled
    public String filebody {get;set;}
    @auraenabled
    public String fileType {get;set;}
    @auraenabled
    public String fileName {get;set;}
    @auraenabled
    public String fileBaseUrl {get;set;} //To download the file uploaded
    
    public MeetingData1(){
        this.participantList = new List<Participant>();
        this.filebody = fileType = fileName = fileBaseUrl = '' ;
    }
    public MeetingData1(String subject, String frequency, String meetingType, String description,
                       String startDateTime, String endDateTime, Double duration, String meetingStatusType, String location){
                           this.subject = subject;
                           this.frequency = frequency;
                           this.meetingType = meetingType;
                           this.description = description;
                           this.startDateTime = startDateTime;
                           this.endDateTime = endDateTime;
                           this.duration = duration;
                           this.meetingStatusType = meetingStatusType;
                           this.location = location;
                       }
}