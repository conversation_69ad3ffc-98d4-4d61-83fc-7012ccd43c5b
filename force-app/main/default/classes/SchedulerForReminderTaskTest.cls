@IsTest
private class SchedulerForReminderTaskTest {

	@TestSetup
	private static void setupData() {
		TestDataFactory.createAccounts(1);

		Account a = [Select id,name from Account LIMIT 1];

		List<Contact> Lstcon = TestDataFactory.craeteContact(1,a.Id);
		Contact con = [Select id,AccountId from Contact where id=:Lstcon[0].id];

		Profile p = [select Id,name from Profile where Name in ('Customer Community User') limit 1];
		User newUser = new User(
			profileId = p.id,
			username = 'newUser'+'Test'+'@yahoo.com',
			email = '<EMAIL>',
			emailencodingkey = 'UTF-8',
			localesidkey = 'en_US',
			languagelocalekey = 'en_US',
			timezonesidkey = 'America/Los_Angeles',
			alias='nuser',
			lastname='lastname');
		newUser.contactID = con.id;
		Insert newUser;
		TestDataFactory.createAuditConfigurations(1);
		Audit_Configuration_List__c aucoListRec =  [  Select id,AccountId__c from Audit_Configuration_List__c limit 1];
		aucoListRec.AccountId__c=con.AccountId;
		aucoListRec.TrainingProgram__c = newUser.id;
		update aucoListRec;

		Audit_Process__c objprocess =  new Audit_Process__c();
		objprocess.Account__c=con.AccountId;
		objprocess.Name='test';
		insert objprocess;

		Audit_Calendar__c objAuditCalendar = new Audit_Calendar__c();
		objAuditCalendar.Account__c=con.AccountId;
		objAuditCalendar.Auditee__c=newUser.Id;
		objAuditCalendar.Audit_Auditee__c = newUser.Id;
		objAuditCalendar.Process__c=objprocess.Id;
		objAuditCalendar.Audit_Auditor__c=newUser.Id;
		objAuditCalendar.Process_Type__c ='Process and Clauses';
		objAuditCalendar.audit_group__c = newUser.id+';'+newUser.id;
		objAuditCalendar.Schedule_Data__c = Date.today().addDays(2);
		objAuditCalendar.Remind_x_days_before__c = 2;
		insert objAuditCalendar;
	}
	@IsTest
	private static void testBehavior() {

		System.Test.startTest();
		System.schedule('Scheduled Job', '0 0 13 ? * 1', new schedulerForReminderTask());
		System.Test.stopTest();
	}
}