public with sharing class Employee {
    @auraenabled
    public String employeeName {get;set;}
    @auraenabled
    public String hireDate {get;set;} //Actual date
    @auraenabled
    public String jobTitle {get;set;}
    @auraenabled
    public String department {get;set;}
    @auraenabled
    public String supervisor {get;set;}
    @auraenabled
    public String jobtitleEmployee {get;set;}
    @auraenabled
    public String OrgLocationEmployee {get;set;}

    @auraenabled
    public Boolean w4 {get;set;}
    @auraenabled
    public Boolean empEligibility {get;set;}
    @auraenabled
    public Boolean empBenefit {get;set;}
    @auraenabled
    public Boolean corpPolicies {get;set;}
    @auraenabled
    public Boolean safetyRules {get;set;}
    @auraenabled
    public Boolean emergencyProc {get;set;}
    @auraenabled
    public Boolean sexualHarassPolicy {get;set;}
    @auraenabled
    public Boolean inscEnrollmentPlan {get;set;}
    @auraenabled
    public Boolean jobDesc {get;set;}
    @auraenabled
    public Boolean empIllness {get;set;}
    @auraenabled
    public Boolean empHandbook {get;set;}
    @auraenabled
    public Boolean empTrainingForm {get;set;}
    @auraenabled
    public Boolean tour {get;set;}
    @auraenabled
    public Boolean introToWorkGroup {get;set;}
    @auraenabled
    public Boolean dressCode {get;set;}
    @auraenabled
    public Boolean introToIso {get;set;}
    @auraenabled
    public Boolean qualityManual {get;set;}
    @auraenabled
    public Boolean orgChart {get;set;}
    @auraenabled
    public Boolean qualityPolicy {get;set;}
    @auraenabled
    public Boolean jobRelatedWorkInstr {get;set;}
    @auraenabled
    public Boolean payrollStatus {get;set;}
    @auraenabled
    public Boolean empMaintenance {get;set;}
    @auraenabled
    public Boolean newHireRep {get;set;}
    @auraenabled
    public Boolean everifyProcessed {get;set;}
    @auraenabled
    public Boolean medicalInsurance {get;set;}
    @auraenabled
    public Boolean dentalInsurance {get;set;}
    @auraenabled
    public Boolean lifeInsurance {get;set;}
    @auraenabled
    public Boolean x401KEntered {get;set;}
    @auraenabled
    public Boolean payrollSetup {get;set;}
    @auraenabled
    public Boolean releaseOfEmpRec {get;set;}
    @auraenabled
    public Boolean releaseOfMedRec {get;set;}
    @auraenabled
    public Boolean incidentalRelease {get;set;}
    @auraenabled
    public Boolean isUser {get;set;}
    @auraenabled
    public List<String> trainingTopic {get;set;}
    
    @AuraEnabled
    public String firstName{get;set;}
    @AuraEnabled
    public String lastName{get;set;} 
    @AuraEnabled
    public String name{get;set;} 
    @AuraEnabled
    public String email{get;set;}
    @AuraEnabled
    public String phone{get;set;}
    @AuraEnabled
    public String code{get;set;}
    @AuraEnabled
    public String city{get;set;}
    @AuraEnabled
    public String state{get;set;}
    @AuraEnabled
    public String zipCode{get;set;}
    @AuraEnabled
    public String mailingAddress{get;set;}
    @AuraEnabled
    public String country{get;set;}
}