public with sharing class SetAssetNameTriggerHandler {

    public static void changeAssestName(List<Asset__c> newAssestList)
    {
        try{
        List<Asset__c> assetlst = new List<Asset__c>();
        if(AccessibilityManager.isAccessible('Asset__c')) {
            assetlst = [select id,Name,Name__c,Account__c from Asset__c where Account__c =: CMT_AuditConfiguratorLWCCtrl.getAccountId() order by CreatedDate desc limit 1];
        }
        Integer lastAssetNo = 0;
        if(assetlst.size() != 0 && assetlst[0].Name__c != null){
            List<String> assetName_ary = assetlst[0].Name__c.split('-');
            if(assetName_ary.size() > 1){
                lastAssetNo = Integer.valueOf(assetName_ary[1]);
            }
        }
        for(Asset__c ob : newAssestList){
            lastAssetNo = lastAssetNo + 1;
            ob.Name__c = 'AST-' + String.valueOf(lastAssetNo).leftPad(5, '0');
        }
        
    }catch(Exception e){
        
    }

    }
}