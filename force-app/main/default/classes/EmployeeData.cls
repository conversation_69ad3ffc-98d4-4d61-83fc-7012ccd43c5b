public with sharing class EmployeeData {
    @auraenabled
    public String employeeId {get;set;}
    @auraenabled
    public String employeeName {get;set;}
    @auraenabled
    public String superwiserId {get;set;}
    @auraenabled
    public String superwisername {get;set;}
    public EmployeeData(){
        
    }
    public EmployeeData(String employeeId){
        this.employeeId = employeeId;
    }
    public EmployeeData(String employeeId,String superwiserId,String superwisername){
        this.employeeId = employeeId;
        this.superwiserId = superwiserId;
        this.superwisername = superwisername;
    }
    
    public EmployeeData(String employeeId,String superwiserId,String superwisername, String employeeName){
        this.employeeId = employeeId;
        this.superwiserId = superwiserId;
        this.superwisername = superwisername;
        this.employeeName = employeeName;
    }
}