public with sharing class TableConfig {
    @auraenabled
    public Boolean showCheckbox;
    @auraenabled
    public Integer recordPerPage {get;set;}
    @auraenabled
    public Integer count {get;set;}
    @auraenabled
    public Integer offset {get;set;}
    @auraenabled
    public String title;
    @auraenabled
    public Boolean showSearch;
    @auraenabled
    public Boolean isEditable;
    @auraenabled
    public Boolean isFilterable;
    @auraenabled
    public List<DataRow> dataRowList;
    @auraenabled
    public List<DataRow> allDataList;
    @auraenabled
    public List<ColumnData> columnList;
    @AuraEnabled
    public List<filter> filters;
    @auraenabled
    public String objectName {get;set;}
    @auraenabled
    public String componentName;
    
    public TableConfig () {}
    public TableConfig(List<DataRow> dataRowList, List<ColumnData> columnList, Boolean showCheckbox, Boolean showSearch, Boolean isEditable, List<filter> filterList)
    {
        this.dataRowList = dataRowList;
        this.columnList = columnList;
        this.showCheckbox = showCheckbox;
        this.showSearch = showSearch;
        this.isEditable = isEditable;
        this.filters = filterList;
    }
}