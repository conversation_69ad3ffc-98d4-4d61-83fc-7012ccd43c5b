global class SchedulerForReminderTask implements Schedulable {
    global void execute(SchedulableContext sc) {
        sendTheEmail();
    }
    

    public static void sendTheEmail() {
        List<Audit_Calendar__c> audCalList = [
            SELECT Remind_x_days_before__c, Schedule_Data__c, Schedule_Date__c, Audit_Auditee__c, Audit_Auditor__c
            FROM Audit_Calendar__c
            WHERE Remind_x_days_before__c != NULL
        ];
        
        DateTime currentTime = System.now();
        Date myDate = Date.newinstance(currentTime.year(), currentTime.month(), currentTime.day());
        for (Audit_Calendar__c record : audCalList) {
            
            
            Date myDate2 = Date.newinstance(record.Schedule_Date__c.year(), record.Schedule_Date__c.month(), record.Schedule_Date__c.day());
            
            if (myDate == myDate2.addDays(-Integer.valueOf(record.Remind_x_days_before__c))) {
                
                Set<String> setId = new Set<String>();
                if ( ! CMT_Utils.isStringBlank(record.Audit_Auditee__c)) {
                    setId.addAll(CMT_Utils.generateSetFromString(record.Audit_Auditee__c));
                }
                if ( ! CMT_Utils.isStringBlank(record.Audit_Auditor__c)) {
                    setId.addAll(CMT_Utils.generateSetFromString(record.Audit_Auditor__c));
                }
                String htmlBody=CMT_Utils.generateHTMLEmailBodyForInternalAuditReminder(record.Schedule_Date__c,record.Id);
                CMT_Utils.sendCustomEmail(setId, 'Compliance Command Notification', htmlBody, true);
            }
            
        }
    }
}