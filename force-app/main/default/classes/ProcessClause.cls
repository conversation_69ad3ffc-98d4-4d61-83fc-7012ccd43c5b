public with sharing class Process<PERSON>lause {
    @auraenabled
    public Decimal aSortNo {get;set;}
    @auraenabled
    public String process_name {get;set;}
    @auraenabled
    public List<Boolean> cross {get;set;}
    @auraenabled
    public Map<String,Boolean> crossMap {get;set;}
    @auraenabled
    public String compliance {get;set;}
    @auraenabled
    public String Id {get;set;}
    @auraenabled
    public Map<String,String> checklistAndParentNode {get;set;}
    @auraenabled
    public Map<String,List<String>> parentNodeAndChecklists {get;set;}

}