/**
 * @description       : 
 * <AUTHOR> <PERSON><PERSON><PERSON> 
 * @group             : 
 * @last modified on  : 06-23-2023
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
public without sharing class UtilsWithoutSharing {


    public static Integer getInProgressTaskCount(String recordId) {
        // Aggregate query on Tasks for the given recordId and where Status is "Open"
        AggregateResult[] groupedResults = [SELECT COUNT(Id) cnt 
                                            FROM Task
                                            WHERE WhatId = :recordId
                                            AND Status = 'Open'];

        // Return the count of the Tasks. As AggregateResult is a complex object, it needs to be type casted to Integer
        return (Integer)groupedResults[0].get('cnt');
    }

    public static void reopenAllTasksByRecordId(String recordId) {
        List<Task> tasksToUpdate = new List<Task>([SELECT OwnerId, Subject, Description, Status, WhatId 
                                            FROM Task 
                                            WHERE WhatId = :recordId]);
        
        // Update the tasks without sending emails to users
        if(!tasksToUpdate.isEmpty()) {

            for (Task task : tasksToUpdate) {
                task.Status = 'Open';
            }
            
            Database.DMLOptions dml = new Database.DMLOptions();
            dml.EmailHeader.triggerUserEmail = false;
            List<Database.saveResult> srList = Database.update(tasksToUpdate, dml);
            for (Database.SaveResult sr : srList) {
                if (sr.isSuccess()) {
                    // Operation was successful, so get the ID of the record that was processed
                    System.debug('Successfully updated task. Task ID: ' + sr.getId());
                }
                else {
                    // Operation failed, so get all errors                
                    for(Database.Error err : sr.getErrors()) {
                        System.debug('The following error has occurred.');                    
                        System.debug(err.getStatusCode() + ': ' + err.getMessage());
                        System.debug('Tasks fields that affected this error: ' + err.getFields());
                    }
                }
            }
        }
    }

    public static void shareDocumentsWithRelaventUsers(Id contentDocumentId, Set<Id> userIds) {

        // Checking if the current user is owner of the file then skip the sharing
        ContentDocument contentDocumentObj = [SELECT Id, OwnerId FROM ContentDocument WHERE Id = :contentDocumentId];
        if(UserInfo.getUserId() != contentDocumentObj.OwnerId) {
            // Fetching existing ContentDocumentLink records
            Map<Id, ContentDocumentLink> existingLinksMap = new Map<Id, ContentDocumentLink>();
            for(ContentDocumentLink cdl : [SELECT LinkedEntityId, ContentDocumentId, ShareType
                                            FROM ContentDocumentLink 
                                            WHERE 
                                            ShareType != 'I'
                                            AND ContentDocumentId = :contentDocumentId 
                                            AND LinkedEntityId IN :userIds]) {
                existingLinksMap.put(cdl.LinkedEntityId, cdl);
            }
            List<ContentDocumentLink> cdlListToInsert = new List<ContentDocumentLink>();
            for(Id userId : userIds) {
                // Only adding new records for users that don't have an existing link
                if(!existingLinksMap.containsKey(userId)) {
                    ContentDocumentLink cdl = new ContentDocumentLink();
                    cdl.LinkedEntityId = userId;
                    cdl.ShareType = 'C';
                    cdl.Visibility = 'AllUsers';
                    cdl.ContentDocumentId = contentDocumentId;
                    cdlListToInsert.add(cdl);
                }
            }
            if(!cdlListToInsert.isEmpty()) {
                insert cdlListToInsert;
            }
        }
    }


    public static void deleteTherecord(Id recordIdTodelete){
        try{
            
            if(AccessibilityManager.isDeletable('Job_Title__c')){
                Database.delete(recordIdTodelete,false);
            
            }
        }
        catch (Exception e) {
           // System.debug('exception'+e);
            throw new AuraHandledException(e.getMessage());
        }
    }
    public static void deleteTherecordOrgLocation(Id recordIdTodelete){
        try{
                Database.delete(recordIdTodelete,false);
            
        }
        catch (Exception e) {
            //System.debug('exception'+e);
            throw new AuraHandledException(e.getMessage());
        }
    }
    public static void deleteTherecordDepartment(Id recordIdTodelete){
        try{
            
            if(AccessibilityManager.isDeletable('Department__c')){
                Database.delete(recordIdTodelete,false);
            
            }
        }
        catch (Exception e) {
            //System.debug('exception'+e);
            throw new AuraHandledException(e.getMessage());
        }
    }
    
    public static String deleteTherecordGenric(String recordIdTodelete,String sObjectName){
        try{
            
            if(AccessibilityManager.isDeletable(sObjectName)){
                Database.delete(recordIdTodelete,false);
                return 'true';
            }
        }
        catch (Exception e) {
            System.debug('here in the delete the record Genric'+e);
            throw new AuraHandledException(e.getMessage());
            //return 'false';
        }
        return 'false';
    }

    public static list<User> getUsersWithIdList(List<Id> userIds) {
        return [SELECT Id, Username, Email FROM User WHERE Id IN :userIds];
    }
}