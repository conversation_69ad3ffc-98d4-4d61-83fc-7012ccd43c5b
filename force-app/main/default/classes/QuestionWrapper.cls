public with sharing class Question<PERSON>rapper {
    @auraenabled
        public String question {get;set;}
        @auraenabled
        public String clause_no {get;set;}
        @auraenabled
        public String status {get;set;}
        @auraenabled
        public String feedback {get;set;}
        @auraenabled
        public Boolean car {get;set;}
        @auraenabled
        public Boolean car_disable {get;set;}
        @auraenabled
        public String car_no {get;set;}
        @auraenabled
        public String res_id {get;set;}
        @auraenabled
        public String que_id {get;set;}
        @auraenabled
        public String ac_id {get;set;}
        @auraenabled
        public String car_id {get;set;}
        @auraenabled
        public Boolean success {get;set;}
        @auraenabled
        public Boolean failed {get;set;}
        @auraenabled
        public String account_id {get;set;}
        @auraenabled
        public Boolean addQue {get;set;}
        @auraenabled
        public String Name {get;set;}
        @auraenabled
        public String auditStatus {get;set;}
        @auraenabled
        public String processType {get;set;}
        @auraenabled
        public String ca_name {get;set;}
        @auraenabled
        public String nc_name {get;set;}
        @auraenabled
        public String parent_node {get;set;}
        @auraenabled
        public String description {get;set;}
}