public with sharing class documentWrap {
    @AuraEnabled 
    public boolean DocCordinator{get;set;}
    @auraenabled
    public boolean isCurrentUserDocumentCordinator{get;set;}
    @auraenabled
    public boolean isCurrentUserApprover{get;set;}
    @auraenabled
    public boolean isCurrentUserReviewer{get;set;}
    @auraenabled
    public boolean isCurrentUserAuthor{get;set;}
    @auraenabled
    public boolean isReviewerAlreadyApproved{get;set;}
    @auraenabled
    public boolean isCurrentUserClientAdmin{get;set;}

    public documentWrap() {
        this.DocCordinator = false;
        this.isCurrentUserDocumentCordinator = false;
        this.isCurrentUserApprover = false;
        this.isCurrentUserReviewer = false;
        this.isCurrentUserAuthor = false;
        this.isReviewerAlreadyApproved = false;
        this.isCurrentUserClientAdmin = false;
    }
}