public with sharing class EmployeeTraining {
    @auraenabled
    public String employeeId {get;set;} //Id of the selected Employee
    @auraenabled
    public String trainingId {get;set;} //Id of the selected Employee
    @auraenabled
    public String employeeTitle {get;set;} 
    @auraenabled
    public String employeeNo {get;set;} 
    @auraenabled
    public String hireDate {get;set;}
    @auraenabled
    public String trainingDate {get;set;}
    @auraenabled
    public String trainingTime {get;set;}
    @auraenabled
    public String trainer {get;set;}
    @auraenabled
    public String provider {get;set;} //
    @auraenabled
    public String completionDate {get;set;}
    @auraenabled
    public String trainingNeed {get;set;} //Id of Training Skill
    @auraenabled
    public String emailAddress {get;set;}
    @auraenabled
    public String comments {get;set;}
    @auraenabled
    public Boolean isRequest {get;set;}
    @auraenabled
    public Boolean isGroupTraining {get;set;}
    @auraenabled
    public String status {get;set;}
    @auraenabled
    public String comment {get;set;}
    @auraEnabled
    public Boolean trainingeffective {get;set;}
    @auraEnabled
    public Boolean closeTraining {get;set;}
    @auraEnabled
    public Boolean isNewTraining {get;set;}
    @auraEnabled
    public String feedback {get;set;}
    @auraEnabled
    public String supervisor {get;set;}
    @auraEnabled
    public String TrainingRequestedBy {get;set;}
    @auraEnabled
    public String supervisorname {get;set;}
    
    @auraEnabled
    public String TrainingName {get;set;}
    @auraEnabled
    public String course {get;set;}
    @auraEnabled
    public String CourseName {get;set;}
    @auraEnabled
    public String trainingFormType {get;set;}
    @auraEnabled
    public String Emploeenames {get;set;}
    @auraEnabled
    public String duration {get;set;}
    public EmployeeTraining(){
        
    }
    public EmployeeTraining(String employeeId, String employeeTitle, String employeeNo, String hireDate, String trainingDate,
                            String provider, String completionDate, String trainingNeed, String emailAddress, String comments,Boolean isRequest,Boolean isGroupTraining, string duration){
                                this.employeeId = employeeId;
                                this.employeeNo = employeeNo;
                                this.employeeTitle = employeeTitle;
                                this.hireDate = hireDate;
                                this.trainingDate = trainingDate;
                                this.provider = provider;
                                this.completionDate = completionDate;
                                this.trainingNeed = trainingNeed;
                                this.emailAddress = emailAddress;
                                this.comments = comments;
                                this.isRequest = isRequest;
                                this.isGroupTraining = isGroupTraining;
                                this.duration = duration;
                            }
    
    public EmployeeTraining(String employeeId, String trainingId, String employeeTitle, String employeeNo, String hireDate, 
                            String trainingDate, String trainer, String comments,string supervisor,String comment,boolean trainingeffective,String status,String supervisorname,String TrainingRequestedBy){
                                this.employeeId = employeeId;
                                this.trainingId = trainingId;
                                this.employeeNo = employeeNo;
                                this.employeeTitle = employeeTitle;
                                this.hireDate = hireDate;
                                this.trainingDate = trainingDate;
                                this.comments = comments;
                                this.trainer = trainer;
                                this.supervisor = supervisor;
                                this.comment = comment;
                                this.trainingeffective = trainingeffective;
                                this.status = status;
                                this.supervisorname = supervisorname;
                                this.TrainingRequestedBy = TrainingRequestedBy;
                            }
}