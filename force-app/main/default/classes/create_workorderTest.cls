@isTest
public class create_workorderTest {
    
    @isTest
    public static void beforeTest() {
        test.startTest();
        TestDataFactory.createAssetScheduleMaintainance(1);
        List<Asset_Scheduled_Maintenance__c> asm = [select id,Start_From_Date__c,Account__c,Asset__c,Prepared_For__c,Activity_Description__c from Asset_Scheduled_Maintenance__c LIMIT 1];
        create_workorderHandler.createWorkOrder(asm);
        test.stopTest();
        Work_Order__c wo = [select id , Work_Order_Type__c from Work_Order__c LIMIT 1];
        system.assertEquals('Scheduled Maintenance', wo.Work_Order_Type__c);
    }
}