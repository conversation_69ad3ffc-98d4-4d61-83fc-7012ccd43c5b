public with sharing class SetNCNameTriggerHandler {
    
    public static void changeName(List<NC_Form__c> newNCList, Boolean isInsert)
    {
        try {
            if (isInsert) {
                if(AccessibilityManager.isAccessible('NC_Form__c')) {
                    List<NC_Form__c> incident = [SELECT Id, Name__c FROM NC_Form__c ORDER BY Name__c DESC LIMIT 1];
                    for (NC_Form__c ncl : newNCList) {
                        String recordNumber = getLatestIncidentNumber(incident);
                        ncl.Name__c = 'IN-' + recordNumber.substring(recordNumber.length() - 5);
                    }
                } else {
                    throw new AuraHandledException('No access to object NC_Form__c');
                }
            }
        } catch(Exception e){
            HandleCustomException.LogException(e);
            throw new AuraHandledException(e.getMessage() + ' -- ' + e.getStackTraceString());
        }
    }
    
    public static void checkForDepartmentName(List<NC_Form__c> newNCList, Map<Id,NC_Form__c> oldMap, Boolean isInsert) {
        Set<Id> departmentIdSet = new Set<Id>();
        Map<Id, Department__c> departmentObjMap = new Map<Id, Department__c>();
        for (NC_Form__c ncl : newNCList) {
            if(ncl.cocmd__Department_Lookup__c != null) {
                departmentIdSet.add(ncl.cocmd__Department_Lookup__c);
            }
        }
        
        if(!departmentIdSet.isEmpty() && AccessibilityManager.isAccessible('Department__c')) {
            departmentObjMap = new Map<Id,Department__c>([SELECT Id, Name FROM Department__c WHERE Id IN :departmentIdSet]);
        }

        if (isInsert) { 
            for (NC_Form__c ncl : newNCList) {
                //Updating the Department Name
                if(ncl.cocmd__Department_Lookup__c != null) {
                    Department__c deptObj = departmentObjMap.get(ncl.cocmd__Department_Lookup__c);
                    ncl.cocmd__Department__c = deptObj.Name;
                }
            }
        } else {
            //In Before update
            for (NC_Form__c ncl : newNCList) {
                //Updating the Department Name
                NC_Form__c oldNcForm = oldMap.get(ncl.Id);
                if(ncl.cocmd__Department_Lookup__c != oldNcForm.cocmd__Department_Lookup__c) {
                    if(ncl.cocmd__Department_Lookup__c != null) {
                        Department__c deptObj = departmentObjMap.get(ncl.cocmd__Department_Lookup__c);
                        ncl.cocmd__Department__c = deptObj.Name;
                    } else {
                        ncl.cocmd__Department__c = null;
                    }
                }
            }
        }
    }

    public static void updateDatetoIdentifyandContain(List<NC_Form__c> newNCList, Map<Id,NC_Form__c> oldMap, Boolean isInsert) {
        
        for(NC_Form__c ncObj : newNCList) {

            if(isInsert) {
                if(ncObj.stage__c == 'Identify and Contain') {
                    ncObj.Date_to_Identify_and_Contain__c = System.now().date();
                }
            } else {
                NC_Form__c oldNcForm = oldMap.get(ncObj.Id);
                
                if(oldNcForm.stage__c != ncObj.stage__c && oldNcForm.stage__c == 'Draft' && ncObj.stage__c == 'Identify and Contain') {
                    ncObj.Date_to_Identify_and_Contain__c = System.now().date();
                }
            
                //Resetting the comment added value
                if(oldNcForm.stage__c != ncObj.stage__c) {
                    ncObj.Is_Comment_Added_in_Current_Stage__c = false;
                }
            }
        }
    }
    
    public static String getLatestIncidentNumber (List<NC_Form__c> incident) {
        Integer latestNumber = 1;
        String recordNumber = '00000';

        if(incident.size() > 0) {
            latestNumber += Integer.valueOf(incident[0].Name__c.substringAfter('-').trim());
            recordNumber = '00000' + String.valueOf(latestNumber);
            return recordNumber;
        }
        return '00001';
    }

    public static void updateSubmittedCheckAtIdentifyAndContain(List<NC_Form__c> newNCList, Map<Id,NC_Form__c> oldMap, Boolean isInsert) {
        for(NC_Form__c ncObj : newNCList) {
            if(ncObj.stage__c == 'Identify and Contain') {
                if(isInsert) {
                    ncObj.cocmd__Is_Submitted__c = true;
                } else {
                    NC_Form__c oldNcForm = oldMap.get(ncObj.Id);
                    if(oldNcForm.stage__c != ncObj.stage__c && oldNcForm.stage__c == 'Draft') {
                        ncObj.cocmd__Is_Submitted__c = true;
                    } else if(oldNcForm.stage__c != ncObj.stage__c && oldNcForm.stage__c != 'Draft') {
                        ncObj.cocmd__Is_Submitted__c = false;
                    }
                }
            } else if(!isInsert) {
                NC_Form__c oldNcForm = oldMap.get(ncObj.Id);
                if(oldNcForm.stage__c != ncObj.stage__c && oldNcForm.stage__c == 'Evaluate and Close' && ncObj.stage__c != 'Resolved') {
                    ncObj.cocmd__Is_Submitted__c = false;
                }
            }
        }
    }

    public static void handleEmailNotifications(List<NC_Form__c> newNCList, Map<Id,NC_Form__c> oldMap, Boolean isInsert) {
        for(NC_Form__c ncObj : newNCList) {
            if(isInsert) {
                if(ncObj.stage__c == 'Identify and Contain') {
                    sendNCEmaiIdentifyAndContains(ncObj);
                }
            } else {
                NC_Form__c oldNcForm = oldMap.get(ncObj.Id);
                if(oldNcForm.stage__c != ncObj.stage__c && oldNcForm.stage__c == 'Draft' && ncObj.stage__c == 'Identify and Contain') {
                    sendNCEmaiIdentifyAndContains(ncObj);
                } else if(oldNcForm.stage__c != ncObj.stage__c && oldNcForm.stage__c != 'Draft' && ncObj.stage__c == 'Identify and Contain') {
                    sendNCEmailForReturnToIdentifyAndContain(ncObj);
                } else if(oldNcForm.stage__c != ncObj.stage__c &&  oldNcForm.stage__c == 'Identify and Contain' && ncObj.stage__c == 'Determine Disposition' && ncObj.Is_Submitted__c == false) {
                    sendNCEmailForResubmittedFromIdentifyAndContains(ncObj);
                } else if(oldNcForm.stage__c != ncObj.stage__c &&  oldNcForm.stage__c == 'Determine Disposition' && ncObj.stage__c == 'Correct and Implement') {
                    sendNCEmailForCorrectAndImplement(ncObj);
                } else if(oldNcForm.stage__c != ncObj.stage__c &&  ncObj.stage__c == 'Determine Disposition' && oldNcForm.stage__c != 'Identify and Contain') {
                    sendNCEmailForReturnToDisposition(ncObj);
                } else if(oldNcForm.stage__c != ncObj.stage__c &&  oldNcForm.stage__c == 'Correct and Implement' && ncObj.stage__c == 'Evaluate and Close') {
                    sendNCEmailForEvaluateandClose(ncObj);
                } else if(oldNcForm.stage__c != ncObj.stage__c &&  ncObj.stage__c == 'Correct and Implement' && oldNcForm.stage__c == 'Evaluate and Close') {
                    sendNCEmailForReturnToCorrectAndImplement(ncObj);
                } else if(oldNcForm.stage__c != ncObj.stage__c && oldNcForm.stage__c == 'Evaluate and Close' && ncObj.stage__c == 'Resolved') {
                    sendNCEmailForResolved(ncObj);
                }
                
            }
        }
    }

    @TestVisible private static void sendNCEmaiIdentifyAndContains(NC_Form__c Ncob){

        List<String> receiverIds = new List<String>();            
        Audit_Configuration_List__c config = CMT_Utils.getAuditConfiguration();

        if(config != null && String.isNotBlank(config.NCAuthority__c)){ 
            receiverIds = config.NCAuthority__c.split(';');
        }
        String emailBody = '<html><p>New Incident record has been created. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__ncId='+ Ncob.Id +'">' + Ncob.Name__c + '</a></html>';
        
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);

    }

    @TestVisible private static void sendNCEmailForReturnToIdentifyAndContain(NC_Form__c Ncob) {
        
        List<String> receiverIds = new List<String>();
        receiverIds.add(Ncob.CreatedById); 
        String emailBody = '<html><p>Incident record has been returned to Identify and Contain. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__ncId='+ Ncob.Id +'">' + Ncob.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);

    }

    @TestVisible private static void sendNCEmailForResubmittedFromIdentifyAndContains(NC_Form__c Ncob) {
    
        List<String> receiverIds = new List<String>();
        receiverIds.add(Ncob.Disposition_AuthorisedBy__c); 
        String emailBody = '<html><p>Incident record has been assigned to you. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__ncId='+ Ncob.Id +'">' + Ncob.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);

    }

    @TestVisible private static void sendNCEmailForReturnToDisposition(NC_Form__c Ncob) {
        
        List<String> receiverIds = new List<String>();
        receiverIds.add(Ncob.Disposition_AuthorisedBy__c); 
        String emailBody = '<html><p>Incident record has been returned to Determine Disposition. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__ncId='+ Ncob.Id +'">' + Ncob.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);

    }

    @TestVisible private static void sendNCEmailForCorrectAndImplement(NC_Form__c Ncob) {
        
        List<String> receiverIds = new List<String>();
        receiverIds.add(Ncob.Assigned_To_Correct_and_Implement__c); 
        String emailBody = '<html><p>Incident record has been assigned to you. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__ncId='+ Ncob.Id +'">' + Ncob.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);

    }

    @TestVisible private static void sendNCEmailForReturnToCorrectAndImplement(NC_Form__c Ncob) {
        
        List<String> receiverIds = new List<String>();
        receiverIds.add(Ncob.Assigned_To_Correct_and_Implement__c); 
        String emailBody = '<html><p>Incident record has been returned to Correct and Implement. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__ncId='+ Ncob.Id +'">' + Ncob.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);

    }

    @TestVisible private static void sendNCEmailForEvaluateandClose(NC_Form__c Ncob){

        List<String> receiverIds = new List<String>();            
        Audit_Configuration_List__c config = CMT_Utils.getAuditConfiguration();

        if(config != null && String.isNotBlank(config.NCCAIncharge__c)){ 
            receiverIds = config.NCCAIncharge__c.split(';');
        }
        String emailBody = '<html><p>New Incident record has moved to Evaluate and Close. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__ncId='+ Ncob.Id +'">' + Ncob.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);

    }

    @TestVisible private static void sendNCEmailForResolved(NC_Form__c Ncob){

        List<String> receiverIds = new List<String>();            
        Audit_Configuration_List__c config = CMT_Utils.getAuditConfiguration();

        receiverIds.add(Ncob.CreatedById); 
        receiverIds.add(Ncob.Identified_By__c);
        receiverIds.add(Ncob.Assigned_To_Correct_and_Implement__c); 
        receiverIds.add(Ncob.Assigned_To__c);
        receiverIds.add(Ncob.Disposition_AuthorisedBy__c); 
        receiverIds.add(Ncob.NC_DispositionedBy__c);
        receiverIds.add(Ncob.Disposition_ReinspectedBy__c);
        
        String emailBody = '<html><p>Incident has been resolved. </p> <a href="' + URL.getSalesforceBaseUrl().toExternalForm() + '/s/incident?c__pageName=incident&c__ncId='+ Ncob.Id +'">' + Ncob.Name__c + '</a></html>';
        CMT_Utils.sendCustomEmail(new Set<String>(receiverIds), 'Compliance Command Notification', emailBody, true);

    }

}