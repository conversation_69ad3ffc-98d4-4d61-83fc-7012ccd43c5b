public with sharing class EmployeeTrainingWrapper {
    @auraenabled
    public String empTrainingId {get;set;}
    @auraenabled
    public String employeeId {get;set;}
    @auraenabled
    public String employeeName {get;set;}
    @auraenabled
    public String status {get;set;}
    @auraenabled
    public String feedback {get;set;}
    @auraenabled
    public String feedbackId {get;set;}
    @auraenabled
    public Boolean completed {get;set;}
    public EmployeeTrainingWrapper(){
        completed = true;
    }
}