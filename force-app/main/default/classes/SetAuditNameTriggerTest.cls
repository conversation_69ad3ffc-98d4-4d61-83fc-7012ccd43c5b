@isTest
private class SetAuditNameTriggerTest {
        
    @isTest
    static void testSetAuditNameTrigger() {
        // Create test data
       TestDataFactory.createAccounts(1);
        
        Account a = [Select id,name from Account LIMIT 1];
        
        List<Contact> Lstcon = TestDataFactory.craeteContact(1,a.Id);
        Contact con = [Select id,AccountId from Contact where id=:Lstcon[0].id];
        
        Profile p = [select Id,name from Profile where Name in ('Customer Community Plus User') limit 1];
        User newUser = new User(
            profileId = p.id,
            username = 'newUser'+'Test'+'@yahoo.com',
            email = '<EMAIL>',
            emailencodingkey = 'UTF-8',
            localesidkey = 'en_US',
            languagelocalekey = 'en_US',
            timezonesidkey = 'America/Los_Angeles',
            alias='nuser',
            lastname='lastname');
        newUser.contactID = con.id;
        Insert newUser;
        TestDataFactory.createAuditConfigurations(1);
        Audit_Configuration_List__c aucoListRec =  [  Select id,AccountId__c from Audit_Configuration_List__c limit 1];
        aucoListRec.AccountId__c=con.AccountId;
        aucoListRec.TrainingProgram__c = newUser.id;
        update aucoListRec;
        
        TestDataFactory.createAuditCalender(3);

        List<Audit_Calendar__c> testAuditCalendars = new List<Audit_Calendar__c>();
        for (Integer i = 0; i < 3; i++) {
            Audit_Calendar__c ac = new Audit_Calendar__c(
                Account__c = a.Id,
                Name__c = 'Test',
                Time__c = Time.newInstance(12, 0, 0, 0),
            	Audit_Status__c = 'Completed'
            );
            testAuditCalendars.add(ac);
        }
        insert testAuditCalendars;
        

        // Verify that the names were changed according to the logic in SetAuditNameTriggerHandler
        List<Audit_Calendar__c> updatedAuditCalendars = [SELECT Id, Name__c FROM Audit_Calendar__c WHERE Id IN :testAuditCalendars];
        System.assertEquals(3, updatedAuditCalendars.size(), 'Expected 3 Audit Calendars to be updated');
        System.assertEquals('AUD-00001', updatedAuditCalendars[0].Name__c);
        System.assertEquals('AUD-00002', updatedAuditCalendars[1].Name__c);
        System.assertEquals('AUD-00003', updatedAuditCalendars[2].Name__c);
    }
}