    public with sharing class Employ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {
        public static void onAfterInsert(List<Employee__c> records) {
            assignTrainingToEmployeesForJobTitle(records);
            assignTrainingToEmployeesForDepartment(records);
        }

        public static void assignTrainingToEmployeesForJobTitle(List<Employee__c> records){
            Map<Id,List<cocmd__Course_Job_Title__c>> mapjobTitleCourse = new Map<Id,List<cocmd__Course_Job_Title__c>>();
            Set<Id> jobtitles=new Set<Id>();
            for(Employee__c emp : records){
                jobTitles.add(emp.Job_Title_Employee__c);
            }
            Set<Id> courseIdSet = new Set<Id>();
            Set<id> courseIdActiveSet = new Set<Id>();
            List<cocmd__Training_Course__c> nonActiveCourses = [SELECT Id, cocmd__Schedule_for_Selected_Job_Titles__c FROM cocmd__Training_Course__c WHERE cocmd__Schedule_for_Selected_Job_Titles__c =:true];
            for(cocmd__Training_Course__c tt: nonActiveCourses){
                courseIdActiveSet.add(tt.Id);
            }
            
            if (AccessibilityManager.isAccessible('Course_Job_Title__c') ){

                for(cocmd__Course_Job_Title__c junction:[SELECT Id, cocmd__Training_Course__c, cocmd__Job_Title__c FROM cocmd__Course_Job_Title__c WHERE cocmd__Job_Title__c IN :jobTitles AND cocmd__Training_Course__c IN:nonActiveCourses]){
                    
                    courseIdSet.add(junction.cocmd__Training_Course__c);
                    if(!mapjobTitleCourse.containsKey(junction.cocmd__Job_Title__c)){
                        mapjobTitleCourse.put(junction.cocmd__Job_Title__c,new List<cocmd__Course_Job_Title__c>());
                    }
                    
                    mapjobTitleCourse.get(junction.cocmd__Job_Title__c).add(junction);
                }
            }
        
            List<cocmd__Employee_Training__c> employeeTrainingList = new List<cocmd__Employee_Training__c>();
        
            List<cocmd__Training_Management__c> trainingManagementList = new List<cocmd__Training_Management__c>();
            for(Employee__c emp:records){
                
                if(emp.cocmd__Job_Title_Employee__c!=null){
                    if(mapjobTitleCourse.get(emp.cocmd__Job_Title_Employee__c) !=null){
                
                        for(cocmd__Course_Job_Title__c jec:mapjobTitleCourse.get(emp.cocmd__Job_Title_Employee__c)){
                            if(courseIdActiveSet.contains(jec.cocmd__Training_Course__c)){
                                cocmd__Training_Management__c train=new cocmd__Training_Management__c(cocmd__Training_Course__c=jec.cocmd__Training_Course__c,cocmd__Account__c=emp.cocmd__Account__c);
                                trainingManagementList.add(train);
                            }           
                        }
                    }
                }
                if(trainingManagementList.size()!=null){  
                    if (AccessibilityManager.isCreateable('Training_Management__c') ){

                    insert trainingManagementList;
                    }
                }
                Map<Id,Id> mapcourseTrainingManagement = new Map<Id,Id>();
                for(Training_Management__c tac:trainingManagementList){
                    mapcourseTrainingManagement.put(tac.cocmd__Training_Course__c,tac.Id);
                }
                if(mapjobTitleCourse.containsKey(emp.cocmd__Job_Title_Employee__c)) {
                    for(cocmd__Course_Job_Title__c jec:mapjobTitleCourse.get(emp.cocmd__Job_Title_Employee__c)){
                    
                        cocmd__Employee_Training__c employeeTraining =new cocmd__Employee_Training__c();
                        employeeTraining.cocmd__Course_Id__c = jec.cocmd__Training_Course__c;
                        employeeTraining.cocmd__Training_Management__c = mapcourseTrainingManagement.get(jec.cocmd__Training_Course__c);
                        employeeTraining.cocmd__Employee__c = emp.Id;
                        employeeTraining.cocmd__Account__c = emp.cocmd__Account__c;

                        employeeTrainingList.add(employeeTraining);
                        
                    }
                }
                if(employeeTrainingList.size()!=null){
                    if (AccessibilityManager.isCreateable('Employee_Training__c') ){

                    insert employeeTrainingList;
                    }
                }

            }
        }

        public static void assignTrainingToEmployeesForDepartment(List<Employee__c> records){
            Map<Id,List<cocmd__Course_Department__c>> mapdepCourse = new Map<Id,List<cocmd__Course_Department__c>>();
            Set<Id> departments=new Set<Id>();
            for(Employee__c emp : records){
                departments.add(emp.Department__c);
            }
            Set<Id> courseIdSet = new Set<Id>();
            Set<Id> courseIdSet2 = new Set<Id>();
            Set<id> courseIdActiveSet = new Set<Id>();
            List<cocmd__Training_Course__c> nonActiveCourses = [SELECT Id, cocmd__Automatically_Schedule_for_Selected_Depa__c FROM cocmd__Training_Course__c WHERE cocmd__Automatically_Schedule_for_Selected_Depa__c =:true];
            for(cocmd__Training_Course__c tt: nonActiveCourses){
                courseIdActiveSet.add(tt.Id);
            }
            if (AccessibilityManager.isAccessible('Course_Department__c') ){
            
                for(cocmd__Course_Department__c junction:[SELECT Id, cocmd__Training_Course__c, cocmd__Department__c FROM cocmd__Course_Department__c WHERE cocmd__Department__c IN :departments AND cocmd__Training_Course__c IN:nonActiveCourses]){
                
                    courseIdSet.add(junction.cocmd__Training_Course__c);
                    if(!mapdepCourse.containsKey(junction.cocmd__Department__c)){
                        mapdepCourse.put(junction.cocmd__Department__c,new List<cocmd__Course_Department__c>());
                    }
                
                    mapdepCourse.get(junction.cocmd__Department__c).add(junction);
                }
            }
            Map<Id,cocmd__Training_Management__c> mapcourseTM = new Map<Id,cocmd__Training_Management__c>();
            
            if (AccessibilityManager.isAccessible('Training_Management__c') ){

                for(cocmd__Training_Management__c train:[SELECT Id,cocmd__Training_Course__c FROM cocmd__Training_Management__c WHERE cocmd__Training_Course__c IN:courseIdSet]){
                    mapcourseTM.put(train.cocmd__Training_Course__c,train);
                }
            }
            List<cocmd__Employee_Training__c> employeeTrainingList = new List<cocmd__Employee_Training__c>();

            List<cocmd__Training_Management__c> trainingManagementList = new List<cocmd__Training_Management__c>();
            for(Employee__c emp:records){
                
                if(emp.cocmd__Department__c!=null){
                    if(mapdepCourse.get(emp.cocmd__Department__c) !=null){
                
                        for(cocmd__Course_Department__c jec:mapdepCourse.get(emp.cocmd__Department__c)){
                            if(courseIdSet2.contains(jec.cocmd__Training_Course__c)){
                                cocmd__Training_Management__c train=new cocmd__Training_Management__c(cocmd__Training_Course__c=jec.cocmd__Training_Course__c,cocmd__Account__c=emp.cocmd__Account__c);
                                trainingManagementList.add(train);
                            }           
                        }
                    }
                }
                if(trainingManagementList.size()!=null){  
                    if (AccessibilityManager.isCreateable('Training_Management__c') ){
                    
                    insert trainingManagementList;
                    }
                }
                Map<Id,Id> mapcourseTrainingManagement = new Map<Id,Id>();
                for(Training_Management__c tac:trainingManagementList){
                    mapcourseTrainingManagement.put(tac.cocmd__Training_Course__c,tac.Id);
                }
                if(mapdepCourse.containsKey(emp.cocmd__Department__c)) {
                    for(cocmd__Course_Department__c jec:mapdepCourse.get(emp.cocmd__Department__c)){
                    
                        cocmd__Employee_Training__c employeeTraining =new cocmd__Employee_Training__c();
                        employeeTraining.cocmd__Course_Id__c = jec.cocmd__Training_Course__c;
                        employeeTraining.cocmd__Training_Management__c = mapcourseTrainingManagement.get(jec.cocmd__Training_Course__c);
                        employeeTraining.cocmd__Employee__c = emp.Id;
                        employeeTraining.cocmd__Account__c = emp.cocmd__Account__c;

                        employeeTrainingList.add(employeeTraining);
                        
                    }
                }
                if(employeeTrainingList.size()!=null){
                    if (AccessibilityManager.isCreateable('Employee_Training__c') ){

                    insert employeeTrainingList;
                    }
                }

            }
        }


        public static void assignTrainingForLocation(List<Employee__c> records){
            List<cocmd__Training_Course__c> trainCourse=new List<cocmd__Training_Course__c> ();
            if (AccessibilityManager.isAccessible('Training_Course__c') ){
                trainCourse=[SELECT Id , cocmd__Available_in_Locations__c FROM cocmd__Training_Course__c ];
            }
            Map<String,Set<Id>> mapLocationCourse = new Map<String,Set<Id>>();
            for(cocmd__Training_Course__c course:trainCourse){
                Set<String> locationSet = new Set<String>(course.cocmd__Available_in_Locations__c.split(';'));
                for(String loc:locationSet){
                    if(!mapLocationCourse.containsKey(loc)){
                        mapLocationCourse.put(loc,new Set<Id>());
                    }
                    
                    mapLocationCourse.get(loc).add(course.Id);
                }
                
            }

            
            List<String> location = new List<String>();
            List<cocmd__Employee_Training__c> EtList= new List<cocmd__Employee_Training__c>(); 
            Map<Id,cocmd__Training_Management__c> mapcourseTM = new Map<Id,cocmd__Training_Management__c>();
            if (AccessibilityManager.isAccessible('Training_Management__c') ){
                for(cocmd__Training_Management__c train:[SELECT Id,cocmd__Training_Course__c FROM cocmd__Training_Management__c WHERE cocmd__Training_Course__c IN:mapLocationCourse.keySet()]){
                    mapcourseTM.put(train.cocmd__Training_Course__c,train);
                }
            }
            for(Employee__c emp:records){
                location=emp.cocmd__Organization_Location__c.split(';');
                for(String loc:location){
                    Set<Id> courseSetToinsert = new Set<Id>();
                    courseSetToinsert = mapLocationCourse.get(loc);
                    for(Id courseId: courseSetToinsert){
                        cocmd__Employee_Training__c empTrain= new cocmd__Employee_Training__c();
                        empTrain.cocmd__Course_Id__c = courseId;
                        empTrain.cocmd__Training_Management__c = mapcourseTM.get(courseId).Id;
                        empTrain.cocmd__Employee__c = emp.Id;
                        empTrain.cocmd__Account__c = emp.cocmd__Account__c;
                        EtList.add(empTrain);
                    }
                    
                    
                }

            }
            if (AccessibilityManager.isCreateable('Employee_Training__c') ){

            insert EtList;
            }

        }
    }