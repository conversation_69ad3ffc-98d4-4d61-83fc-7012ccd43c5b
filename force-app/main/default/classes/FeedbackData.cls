public with sharing class FeedbackData {
    @auraenabled
        public String feedback {get;set;}
        @auraenabled
        public String feedbackId {get;set;}
        /*@auraenabled
        public String employeeName {get;set;}
        @auraenabled
        public String trainingName {get;set;}
        @auraenabled
        public String trainingStatus {get;set;} */
        @auraEnabled
        public String trainingId {get;set;}
        @auraEnabled
        public String rating {get;set;}
        @auraEnabled
        public String userId {get;set;}

        public FeedbackData(){

        }
        public FeedbackData(String feedbackId, String feedback){
            this.feedbackId = feedbackId;
            this.feedback = feedback;
        }
}