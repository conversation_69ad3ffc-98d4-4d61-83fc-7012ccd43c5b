global class SchedulerForRecurringTraining implements Schedulable {
    global void execute(SchedulableContext ctx) {
        // Query Employee_Training__c records that are associated with active Employees
        List<Employee_Training__c> employeeTrainingRecords = [
            SELECT Id, Training_Management__r.cocmd__Scheduled_Date_Time__c, 
                   Training_Management__r.Frequency__c, 
                   Employee__r.IsActive__c
            FROM Employee_Training__c
            WHERE Training_Management__r.cocmd__Scheduled_Date_Time__c <= :System.now()
            AND Employee__r.IsActive__c = true
        ];

        

        // Calculate the next training date and send notifications if needed
        for (Employee_Training__c etRecord : employeeTrainingRecords) {
            DateTime nextTrainingDate;

            if (etRecord.Training_Management__r.Frequency__c == 'one-time') {
                // For one-time frequency, set the next training date to null
                nextTrainingDate = null;
            } else if (etRecord.Training_Management__r.Frequency__c == 'monthly') {
                nextTrainingDate = etRecord.Training_Management__r.cocmd__Scheduled_Date_Time__c.addMonths(1);
            } else if (etRecord.Training_Management__r.Frequency__c == 'quarterly') {
                nextTrainingDate = etRecord.Training_Management__r.cocmd__Scheduled_Date_Time__c.addMonths(3);
            } else if (etRecord.Training_Management__r.Frequency__c == 'yearly') {
                nextTrainingDate = etRecord.Training_Management__r.cocmd__Scheduled_Date_Time__c.addYears(1);
            } else if (etRecord.Training_Management__r.Frequency__c == 'bi-annual') {
                nextTrainingDate = etRecord.Training_Management__r.cocmd__Scheduled_Date_Time__c.addMonths(6);
            }

            if (nextTrainingDate != null) {
                // Update the ScheduledDateTime__c field on the Training_Management__c record
                Date testDate= Date.newinstance(nextTrainingDate.year(),nextTrainingDate.month(),nextTrainingDate.day());
              Date notificationDate = testDate.addDays(-(Integer)etRecord.Training_Management__r.cocmd__Days_Before_Next_Occurrence__c);
              Date today = Date.today();
                if (notificationDate == today) {
                    //email 
                    String htmlBodyForTrainees = CMT_Utils.generateHTMLEmailBodyForTrainees(etRecord.cocmd__Training_Management__c);
                    CMT_Utils.sendCustomEmail(etRecord.Employee__r.Email__c, 'Compliance Command Notification', htmlBodyForTrainees, true);


                }
            }
        }
    }
}
