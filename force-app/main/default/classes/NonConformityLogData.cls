public with sharing class NonConformityLogData {
    
        @auraenabled
        public Date nc_submit_date {get;set;}
        @auraenabled
        public String identifield_by {get;set;}
        @auraenabled
        public String measuring_unit {get;set;}
        @auraenabled
        public String department {get;set;}
        @auraenabled
        public String Send_Notification_To {get;set;}
        @auraenabled
        public String assigned_to {get;set;}
        @auraenabled
        public String assigned_to_correct_and_implement {get;set;}
        @auraenabled
        public String source {get;set;}
        @auraenabled
        public Boolean is_submitted {get;set;}
        @auraenabled
        public String item_no {get;set;}
        @auraenabled
        public String item_description {get;set;}
        @auraenabled
        public String order_no {get;set;}
        @auraenabled
        public Decimal quantity {get;set;}
        @auraenabled
        public String problem_description {get;set;}
        @auraenabled
        public String instructions {get;set;}
        @auraenabled
        public String Comment {get;set;}
        @auraenabled
        public String disposition_authorised_by {get;set;}
        @auraenabled
        public String dispositioned_by {get;set;}
        @auraenabled
        public String reinspect_and_released_by {get;set;}
        @auraenabled
        public Date disposition_authorised_date {get;set;}
        @auraenabled
        public Date dispositioned_date {get;set;}
        @auraenabled
        public Date reinspect_and_released_date {get;set;}
        @auraenabled
        public String status {get;set;}
        @auraenabled
        public String effectiveness {get;set;}
        @auraenabled
        public String stage {get;set;}
        @auraenabled
        public String descriptin_of_non_conformity_contained {get;set;}
        @auraenabled
        public String previous_stage {get;set;}
        @auraenabled
        public String process {get;set;}
        @AuraEnabled
        public string evaluate_send_back {get;set;}
        
        @auraenabled
        public Boolean formal_corrective_action {get;set;}
        @auraenabled
        public String car_no {get;set;}
        
        @auraenabled
        public String responseid {get;set;}
        
        @AuraEnabled
        public String ncDisposition {get;set;}
        
        @AuraEnabled
        public String additionalInstructions {get;set;}
        
        
        
        //For files
        @auraenabled
        public String fileBody {get;set;}
        @auraenabled
        public String fileType {get;set;}
        @auraenabled
        public String fileName {get;set;}
        public NonConformityLogData(){
            this.filebody = fileType = fileName = '';
        }
        
    }