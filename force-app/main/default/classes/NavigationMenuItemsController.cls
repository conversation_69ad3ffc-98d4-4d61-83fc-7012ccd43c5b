/**
 * A basic controller for fetching NavigationMenuItems.
 */
global with sharing class NavigationMenuItemsController {
    public NavigationMenuItemsController() {}

    /**
     * Perform two SOQL queries to determine all the children NavigationMenuItems
     * belonging to the provided menuName and publishedState. This example uses the
     * NavigationLinkSet.MasterLabel for the menuName. One could also use the
     * NavigationLinkSet.Id to ensure referential integrity if the MasterLabel changes.
     * 
     * NavigationLinkSet contains both Draft and Live versions of the NavigationMenuItems.
     * Use the publishedState enumeration to filter between them.
     * 
     * Cacheable = true allows this function to be wired.
     */
    @AuraEnabled(cacheable=true)
    public static List<NavigationMenuItem> getNavigationMenuItems(String menuName, String publishedState) {
        System.assert(menuName != null);
        System.assert(publishedState != null);
        
        String networkId = Network.getNetworkId();
        //System.assert(networkId != null);

        // find the link set belonging to the navigation menuName and active networkId
        List<NavigationLinkSet> linkSets = [
            SELECT Id,MasterLabel
            FROM NavigationLinkSet
            WHERE MasterLabel = :menuName AND NetworkId = :networkId
        ];

        
        if(Test.isRunningTest()){
             linkSets = [SELECT Id,MasterLabel FROM NavigationLinkSet LIMIT 50000];
        }
        //System.assert(linkSets.size() == 1);
        Id linkSetId = linkSets.get(0).Id;
        

        // collect all the menu items belonging to the link set
        // published state is either Draft or Live
        return [SELECT toLabel(Label), Target, Type, DefaultListViewId, AccessRestriction,
                ParentId,
                Position,
                Status,
                TargetPrefs
            FROM NavigationMenuItem
            WHERE NavigationLinkSetId = :linkSetId
            AND Status = :publishedState 
            ORDER BY Position
        ];
    }
}