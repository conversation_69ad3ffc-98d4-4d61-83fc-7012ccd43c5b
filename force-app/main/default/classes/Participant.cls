public with sharing class Participant {
    @AuraEnabled
    public string participantId {get;set;}
    @AuraEnabled
    public string participantName {get;set;}
    @AuraEnabled
    public string name {get;set;}
    @AuraEnabled
    public string email {get;set;}
    @AuraEnabled
    public string userId {get;set;}
    public Participant(){

    }
    public Participant(String participantId){
        this.participantId = participantId;
    }
    public Participant(String participantId, String participantName, String name, String email, String userId){
        this.participantId = participantId;
        this.participantName = participantName;
        this.name = name;
        this.email = email;
        this.userId = userId;
    }
}