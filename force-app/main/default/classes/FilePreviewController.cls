public with sharing class FilePreviewController {
   @AuraEnabled
   public static DataWrapper getFileUrl(Id docId){
       try {
            DataWrapper data = new DataWrapper();
            List<String> docStatusList = new List<String>{'Closed - Approved', 'Obsolete', 'Inactive', 'Active', 'Approved'};

            if('cocmd__Document_Change_Request__c' == docId.getSObjectType().getDescribe().getName()){
                if(AccessibilityManager.isAccessible('Document_Change_Request__c')) {
                    Document_Change_Request__c docChange = [SELECT Status__c, Document__r.Document_Status__c, Document__c, CreatedDate, Current_Revision_No__c FROM Document_Change_Request__c WHERE Id =: docId];
                    if(docChange.Document__r.Document_Status__c== 'Obsolete' ){
                        data.docStatus = 'Obsolete';
                    }
                    else{
                        List<Document_Change_Request__c> revisionDocs = new List<Document_Change_Request__c>();
                        revisionDocs = [SELECT Status__c, Document__r.Document_Status__c, Document__c 
                                        FROM Document_Change_Request__c 
                                        WHERE Document__c =: docChange.Document__c AND Current_Revision_No__c >: docChange.Current_Revision_No__c 
                                            AND Status__c IN :docStatusList ];
                        if(!revisionDocs.isEmpty()){
                            data.docStatus = 'Obsolete';
                        }
                    }
                } else { throw new AuraHandledException('Erorr, Insufficient permission.');}
            }
            else{
                if(AccessibilityManager.isAccessible('Document__c') && AccessibilityManager.isAccessible('Document_Change_Request__c')) {
                    Document__c doc = [SELECT Document_Status__c,(SELECT Status__c FROM Document_Change_Requests__r WHERE Status__c IN :docStatusList ORDER BY Current_Revision_No__c DESC) FROM Document__c WHERE Id =: docId];
                    if(doc.Document_Status__c=='Obsolete'){
                        data.docStatus = 'Obsolete';
                    }
                    if(doc.Document_Change_Requests__r != null && doc.Document_Change_Requests__r.size() > 0) {
                        docId = doc.Document_Change_Requests__r[0].Id;
                    }
                } else { throw new AuraHandledException('Erorr, Insufficient permission.'); }
            }

            List<ContentDocumentLink> docLinks = new List<ContentDocumentLink>();
            if(AccessibilityManager.isAccessible(new List<String>{'LinkedEntityId','ContentDocumentId','Visibility','ShareType'},'ContentDocumentLink')) {
             docLinks = [SELECT LinkedEntityId, ContentDocumentId, Id,ContentDocument.Title, Visibility, ShareType FROM ContentDocumentLink WHERE LinkedEntityId =: docId LIMIT 1];
            } else { throw new AuraHandledException('Erorr, Insufficient permission.'); }

            if(!docLinks.isEmpty()){
                List<ContentDistribution> cds = new List<ContentDistribution>();
                if(AccessibilityManager.isAccessible(new List<String>{'ContentVersionId', 'ContentDocumentId','DistributionPublicUrl'},'ContentDistribution')) {
                    cds = [SELECT ContentVersionId, ContentDocumentId, Id, DistributionPublicUrl FROM ContentDistribution WHERE ContentDocumentId =: docLinks[0].ContentDocumentId];
                } else { throw new AuraHandledException('Erorr, Insufficient permission.');}
                if(!cds.isEmpty()){
                    data.fileUrl = cds[0].DistributionPublicUrl;
                }
                else{
                    List<ContentVersion> versions = new List<ContentVersion>();
                   
                    if(AccessibilityManager.isAccessible(new List<String>{'ContentDocumentId','Title','ContentUrl'},'ContentVersion') && 
                    AccessibilityManager.isAccessible(new List<String>{'ContentVersionId', 'ContentDocumentId','DistributionPublicUrl'},'ContentDistribution') && 
                    AccessibilityManager.isCreateable(new List<String>{'ContentVersionId', 'PreferencesAllowViewInBrowser', 'PreferencesLinkLatestVersion', 'PreferencesNotifyOnVisit', 'PreferencesPasswordRequired', 'PreferencesAllowOriginalDownload'},'ContentDistribution')) {
                      versions = [SELECT Id, ContentDocumentId, Title, ContentUrl FROM ContentVersion WHERE ContentDocumentId =: docLinks[0].ContentDocumentId];

                        if(!versions.isEmpty()){
                            ContentDistribution cd = new ContentDistribution(
                            Name = versions[0].Title, ContentVersionId = versions[0].Id, PreferencesAllowViewInBrowser = true,
                            PreferencesLinkLatestVersion = true, PreferencesNotifyOnVisit = false,
                            PreferencesPasswordRequired = false, PreferencesAllowOriginalDownload = true);
                            cds.add(cd);
                            insert cds;
                            data.fileUrl = [SELECT ContentVersionId, ContentDocumentId, Id, DistributionPublicUrl FROM ContentDistribution WHERE Id =: cds[0].Id].DistributionPublicUrl;
                        }
                    } else { throw new AuraHandledException('Erorr, Insufficient permission.'); }
                }
            }
            return data;
       } catch (Exception e) { throw new AuraHandledException(e.getMessage());}
   }

}
