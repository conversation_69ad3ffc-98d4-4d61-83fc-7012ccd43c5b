public with sharing class create_workorder<PERSON>andler {
    
    public static void createWorkOrder(List<Asset_Scheduled_Maintenance__c> newAssestList)
    {
        List<Work_Order__c> wo_lst = new List<Work_Order__c>();
        for(Asset_Scheduled_Maintenance__c asm: newAssestList){
            Work_Order__c wo = new Work_Order__c();
            wo.RequestedBy__c = UserInfo.getUserId();
            wo.Date_of_Request__c = asm.Start_From_Date__c;
            wo.Account__c = asm.Account__c;
            wo.Asset_Id__c = asm.Asset__c;
            wo.Technician_Assigned__c = asm.Prepared_For__c;
            wo.Work_Description__c = asm.Activity_Description__c;
            wo.Work_Order_Type__c = 'Scheduled Maintenance';
            wo.Status__c = 'Approved';
            if(AccessibilityManager.isCreateable('Work_Order__c') && AccessibilityManager.isAccessible('Asset_Scheduled_Maintenance__c') && 
            AccessibilityManager.isCreateable('Account__c') && AccessibilityManager.isAccessible('Asset__c') ){
            wo_lst.add(wo);
            }
        }
        if(AccessibilityManager.isAccessible('Work_Order__c') && AccessibilityManager.isCreateable('Work_Order__c') && AccessibilityManager.isAccessible('Asset_Scheduled_Maintenance__c') && 
            AccessibilityManager.isupdateable('Account__c') && AccessibilityManager.isUpdateable('Asset__c') ){
        insert wo_lst;
        }
    }

}