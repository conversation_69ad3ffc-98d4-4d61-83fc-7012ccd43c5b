@isTest
public class SetNCNameTriggerTest {
    
    @Future public static void testSetup(){
        
        TestDataFactory.createAccounts(1);
        
        Account a = [Select id,name from Account LIMIT 1];
        
        List<Contact> Lstcon = TestDataFactory.craeteContact(1,a.Id);
        Contact con = [Select id,AccountId from Contact where id=:Lstcon[0].id];
        
        Profile p = [select Id,name from Profile where Name in ('Customer Community User') limit 1];
        User newUser = new User(
            profileId = p.id,
            username = 'newUser'+'Test'+'@yahoo.com',
            email = '<EMAIL>',
            emailencodingkey = 'UTF-8',
            localesidkey = 'en_US',
            languagelocalekey = 'en_US',
            timezonesidkey = 'America/Los_Angeles',
            alias='nuser',
            lastname='lastname');
        newUser.contactID = con.id;
        Insert newUser;
        
        List<NC_Form__c> NCFrmLst = new List<NC_Form__c>();
        NC_Form__c NC = new NC_Form__c (
            Account__c = con.AccountId,Name__c='Test-Tst', Is_Submitted__c = true
        );
        NCFrmLst.add(NC);
        NC_Form__c NC1 = new NC_Form__c (
            Account__c = con.AccountId,Name__c='Test-Tst', stage__c = 'Draft', Is_Submitted__c = false
        );
        NCFrmLst.add(NC1);
        Insert NCFrmLst;
        List<NC_Form__c> ncForms = [Select Id,Account__c,Name__c,Is_Submitted__c,stage__c from NC_Form__c Where Id =: NCFrmLst[0].Id ];
        ncForms[0].stage__c = 'Draft';
        Update ncForms[0];
  
    }
   
    @isTest
    public static void beforeAfterTest() {
        
        testSetup();
        
        Test.startTest();
        Test.stopTest();
        
        User newUser = [Select id,ProfileId,contact.AccountId from User ORDER BY CreatedDate DESC limit 1];
        System.assertEquals(true, newUser != null);
        PermissionSet psObj = [SELECT ID, Name FROM PermissionSet WHERE Name = 'Customer_Community_User_Access_To_Custom_Objects'];
        PermissionSetAssignment psaObj = new PermissionSetAssignment(
            PermissionSetId = psObj.Id,
            AssigneeId = newUser.Id
        );
        insert psaObj;
        System.runAs(newUser) {
            
            
            List<NC_Form__c> NCFrmLst = new List<NC_Form__c>();
            NC_Form__c NC = new NC_Form__c (
                Account__c = newUser.contact.AccountId,Name__c='Test-Tst'
            );
            NCFrmLst.add(NC);
            Insert NCFrmLst;
        } 
        
    }
    
}