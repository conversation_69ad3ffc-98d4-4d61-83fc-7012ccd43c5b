public with sharing class MinutesMeetingData {
    @AuraEnabled
    public String managementReviewId {get;set;}
    @AuraEnabled
    public String title {get;set;}
    @AuraEnabled
    public String description {get;set;}
    @AuraEnabled
    public String comment {get;set;}
    @Auraenabled
    public String filebody {get;set;}
    @auraenabled
    public String fileType {get;set;}
    @auraenabled
    public String fileName {get;set;}
    public MinutesMeetingData(){
        this.managementReviewId = '';
    }
    public MinutesMeetingData(String managementReviewId, String title, String description, String comment, String fileName){
        this.managementReviewId = managementReviewId;
        this.title = title;
        this.description = description;
        this.comment = comment;
        this.fileName = fileName;
    }
}