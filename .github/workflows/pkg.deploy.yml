# This is a basic workflow to help you get started with Actions

name: packaging org depoyment

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  push:
    branches: [ master ]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # Runs a single command using the runners shell
      - name: 'Build and Deploy'
        run: |
          wget https://developer.salesforce.com/media/salesforce-cli/sfdx-linux-amd64.tar.xz
          mkdir sfdx-cli
          tar xJf sfdx-linux-amd64.tar.xz -C sfdx-cli --strip-components 1
          ./sfdx-cli/install
      # Runs a set of commands using the runners shell
      - name: 'Decrypt file'
        run: openssl enc -nosalt -aes-256-cbc -d -in assets/server.key.pkg.enc -out server.key -base64 -K 348162101FC6F7E624681B7400B085EEAC6DF7BD357D4248B52330019C50E6FA -iv BFD3C734366D1A974ECD7FD530D8B489
      - name: 'Authorize to production'
        run: sfdx force:auth:jwt:grant -i 3MVG9X4LnCkfEVViNKrwX2fePV9idGKYnpf7Qcoy5YYpujlDBb01ZTtFZWZhjARMJdpQwoQonJ0CPjy6g6fbx -u <EMAIL> -f server.key -s -a cc_pkg
      - name: 'Validate metadata and Run Tests'
        run: sfdx force:source:deploy -x manifest/package.xml -u cc_pkg -l RunLocalTests
